<template>
  <div class="mod-demo-redis">
    <!-- 顶部操作按钮区域 -->
    <div class="top-actions">
      <el-row :gutter="10" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
          <el-button type="info" @click="downloadFile">下载模板</el-button>
          <el-button type="success" @click="scrollToUploadArea">导入Excel数据表格</el-button>
        </el-col>
      </el-row>

      <!-- 文件上传提示区域 -->
      <div v-if="!state_excel.selectedFile" class="upload-tip-area">
        <el-upload
          class="upload-dragger-area"
          :before-upload="() => false"
          :on-change="handleFileChange"
          :file-list="[]"
          accept=".xlsx,.xls,.xlsm"
          :show-file-list="false"
          drag>
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖拽到此处，或<em>点击上传</em>
          </div>
          <div class="el-upload__tip">
            支持.xlsx/.xls/.xlsm文件
          </div>
        </el-upload>
      </div>

      <!-- 已选择文件显示区域 -->
      <div v-if="state_excel.selectedFile" class="selected-file-area">
        <el-alert
          :title="`已选择文件: ${state_excel.selectedFile.name}`"
          type="success"
          :closable="false"
          show-icon>
          <template #default>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>文件大小: {{ formatFileSize(state_excel.selectedFile.size) }}</span>
              <div>
                <el-button type="primary" size="small" @click="handleFileUpload">确认上传</el-button>
                <el-button size="small" @click="clearSelectedFile">取消</el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>
    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="0" style="height: 100%;">
        <!-- 左侧年份树 -->
        <el-col :span="6" class="tree-panel">
          <h4 style="margin-bottom: 15px; color: #333;">年份筛选</h4>
          <el-tree
            :data="treeData"
            :props="defaultProps"
            accordion
            @node-click="handleNodeClick"
            :expand-on-click-node="false"
            :highlight-current="true" />
        </el-col>

        <!-- 右侧内容区域 -->
        <el-col :span="18" class="content-panel">
          <!-- 查询表单 -->
          <div class="search-form">
            <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
              <el-form-item label="监测点位">
                <el-input
                  style="width: 200px"
                  v-model="state.dataForm.monitoringPoint"
                  placeholder="请输入监测点位"
                  clearable>
                </el-input>
              </el-form-item>
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="state.dataForm.startTime"
                  type="month"
                  placeholder="选择开始年月"
                  format="YYYY-MM"
                  value-format="YYYY-MM">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="结束时间">
                <el-date-picker
                  v-model="state.dataForm.endTime"
                  type="month"
                  placeholder="选择结束年月"
                  format="YYYY-MM"
                  value-format="YYYY-MM">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="search">查询</el-button>
                <el-button @click="resetSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 数据表格 -->
          <div v-if="state.dataList && state.dataList.length > 0">
            <el-table
              v-loading="state.dataListLoading"
              :data="state.dataList"
              border
              @selection-change="state.dataListSelectionChangeHandle"
              style="width: 100%"
              :height="tableHeight">
              <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
              <el-table-column width="220" prop="monitoringPoint" label="监测点位" header-align="center" align="center" fixed="left"></el-table-column>
              <el-table-column prop="year" label="年" header-align="center" align="center" width="80"></el-table-column>
              <el-table-column prop="month" label="月" header-align="center" align="center" width="80"></el-table-column>
              <el-table-column prop="day" label="日" header-align="center" align="center" width="80"></el-table-column>
              <el-table-column prop="transparency" label="透明度(cm)" header-align="center" align="center" width="120"></el-table-column>
              <el-table-column prop="dissolvedOxygen" label="溶解氧(mg/L)" header-align="center" align="center" width="120"></el-table-column>
              <el-table-column prop="waterTemperature" label="水温(℃)" header-align="center" align="center" width="100"></el-table-column>
              <el-table-column prop="ph" label="pH值" header-align="center" align="center" width="80"></el-table-column>
              <el-table-column prop="totalNitrogen" label="总氮(mg/L)" header-align="center" align="center" width="120"></el-table-column>
              <el-table-column prop="ammoniaNitrogen" label="氨氮(mg/L)" header-align="center" align="center" width="120"></el-table-column>
              <el-table-column prop="totalPhosphorus" label="总磷(mg/L)" header-align="center" align="center" width="120"></el-table-column>
              <el-table-column prop="chlorophyllA" label="叶绿素a(μg/L)" header-align="center" align="center" width="140"></el-table-column>
              <el-table-column prop="chemicalOxygenDemand" label="化学需氧量(mg/L)" header-align="center" align="center" width="160"></el-table-column>
              <el-table-column prop="nitriteNitrogen" label="亚硝酸盐氮(mg/L)" header-align="center" align="center" width="160"></el-table-column>
              <el-table-column prop="nitrateNitrogen" label="硝酸盐氮(mg/L)" header-align="center" align="center" width="160"></el-table-column>
              <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
                <template v-slot="scope">
                  <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
                  <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="margin-top: 20px; text-align: right;">
              <el-pagination
                :current-page="state.page"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="state.limit"
                :total="state.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="state.pageSizeChangeHandle"
                @current-change="state.pageCurrentChangeHandle">
              </el-pagination>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else style="text-align: center; padding: 60px 0;">
            <el-empty :description="$t('noData')" :image-size="200" />
          </div>
        </el-col>
      </el-row>
    </div>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, nextTick, onBeforeUnmount } from "vue";
import AddOrUpdate from "./interface-data-shuiku-add-or-update.vue";
import { ElLoading, ElMessage } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
import { useRoute, useRouter } from "vue-router";
const { $t } = globalLanguage();
const route = useRoute();
const router = useRouter();

interface Tree {
  label: string;
  children?: Tree[];
}

const handleNodeClick = (data: { label: string }, getNode: { parent: { label: string, parent?: { label: string } }, data: { children: any[] } }) => {
  const month = getNode.parent.label;
  const year = month ? getNode.parent.parent?.label : undefined;

  if (getNode.data.children.length === 0) {
    if (year === undefined && month === undefined) {
      state.dataForm.year = data.label;
      state.getDataList();
      return;
    }
    if (year === undefined) {
      state.dataForm.year = month;
      state.dataForm.month = data.label;
      state.getDataList();
      return;
    }
    state.dataForm.year = year;
    state.dataForm.month = month;
    state.dataForm.monitoringPoint = data.label;
    state.getDataList();
  }
};

const treeData = reactive<Tree[]>([]);

const getTreeData = () => {
  baseService.get("/task/conditionshuiku/list", { limit: 100000 }).then((res) => {
    const data = res.data.map((item: any) => ({
      label: item.label,
      children: item.children || []
    }));
    treeData.splice(0, treeData.length);
    treeData.push(...data);
  });
};

getTreeData();

const defaultProps = {
  children: "children",
  label: "label"
};

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/interfaceDatashuiku/page",
  getDataListIsPage: true,
  exportURL: "/task/interfaceDatashuiku/export",
  deleteURL: "/task/interfaceDatashuiku",
  uploadURL: "/task/interfaceDatashuiku/upload",
  dataForm: {
    year: "",
    month: "",
    monitoringPoint: "",
    startTime: "",
    endTime: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const tableHeight = ref<number>(400);
const calculateTableHeight = () => {
  const offset = 260;
  const h = window.innerHeight - offset;
  tableHeight.value = h > 200 ? h : 200;
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onMounted(() => {
  if (route.query.monitoringPoint) {
    state.dataForm.monitoringPoint = route.query.monitoringPoint as string;
    if (route.query.from === 'monitoring') {
      nextTick(() => {
        state.getDataList();
      });
    }
  }
});

const search = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.getDataList();
};

const resetSearch = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.dataForm.monitoringPoint = "";
  state.dataForm.startTime = "";
  state.dataForm.endTime = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

const state_excel = reactive({
  selectedFile: null
});

function handleFileChange(file: any, fileList: any) {
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw;
  } else {
    state_excel.selectedFile = null;
  }
}

function handleFileUpload() {
  if (!state_excel.selectedFile) {
    ElMessage.error("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  openLoading();
  baseService
    .post("/task/interfaceDatashuiku/excel-input", formData)
    .then((response) => {
      ElMessage.success("上传成功");
      state.getDataList();
      closeLoading();
      getTreeData();
      state_excel.selectedFile = null;
      updateBgaDataRef.value.fileList = [];
    })
    .catch((error) => {
      console.error("错误:", error);
      ElMessage.error("上传失败，请检查文件格式");
      closeLoading();
      state_excel.selectedFile = null;
      updateBgaDataRef.value.fileList = [];
    });
}

const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};

const closeLoading = () => {
  ElLoading.service().close();
};

const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/InterfaceDataShuikuExcelDataImport.xlsx";
  downloadLink.download = "水质水库数据Excel模板.xlsx";
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 清除选择的文件
const clearSelectedFile = () => {
  state_excel.selectedFile = null;
};

// 滚动到上传区域
const scrollToUploadArea = () => {
  const uploadArea = document.querySelector('.upload-tip-area, .selected-file-area');
  if (uploadArea) {
    uploadArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
</script>

<style scoped>
.mod-demo-redis {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 100px);
}

.top-actions {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.upload-inline {
  display: inline-block;
  margin-left: 10px;
}

.upload-tip-area {
  margin-top: 15px;
}

.upload-dragger-area {
  width: 100%;
}

.upload-dragger-area .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: border-color 0.3s;
}

.upload-dragger-area .el-upload-dragger:hover {
  border-color: #409eff;
}

.selected-file-area {
  margin-top: 15px;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tree-panel {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 20px;
}

.content-panel {
  padding: 20px;
}

.search-form {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.el-radio-button {
  --el-radio-button-checked-bg-color: #edf9f8;
  --el-radio-button-checked-text-color: #17b3a3;
}

::v-deep .month-card .el-radio-button__inner {
  width: 300px;
  height: 60px;
  font-size: 18px;
  padding-top: 20px;
}

::v-deep .el-tree {
  background: transparent;
}

::v-deep .el-tree-node__content {
  height: 36px;
  line-height: 36px;
}

::v-deep .el-tree-node__content:hover {
  background-color: #f0f9ff;
}

::v-deep .el-table {
  border-radius: 6px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}
</style>