<template>
  <div class="mod-demo-redis">
    <el-form :inline="true" :model="state_excel" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="downloadFile">下载模板</el-button>
      </el-form-item>
      <el-upload class="upload-demo" :before-upload="() => false" :on-change="handleFileChange" :file-list="[]" accept=".xlsx,.xls,.xlsm">
        <el-button type="primary">选择文件</el-button>
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">上传文件</el-button>
      </el-form-item>
      <el-upload class="upload-demo" :action="state.uploadURL" :before-upload="() => false" :on-change="handleFileChange" :file-list="[]" accept=".xlsx,.xls,.xlsm">
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
    </el-form>
    <!-- 弹窗, 新增 / 修改 -->
    <div style="display: flex; justify-content: left; align-items: start">
      <el-tree style="max-width: 300px; min-width: 300px" :data="treeData" :props="defaultProps" accordion @node-click="handleNodeClick" />
      <el-main>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" style="margin-top: 20px">
          <el-form-item>
            <el-form-item>
              <el-input style="width: 200px" v-model="state.dataForm.monitoringPoint" placeholder="监测点位" clearable></el-input>
            </el-form-item>
            <!--时间查询-->
            <el-form-item label="开始时间">
              <el-date-picker v-model="state.dataForm.startTime" type="month" placeholder="选择开始年月" format="YYYY-MM" value-format="YYYY-MM"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker v-model="state.dataForm.endTime" type="month" placeholder="选择结束年月" format="YYYY-MM" value-format="YYYY-MM"></el-date-picker>
            </el-form-item>
            <!--时间查询-->
            <el-form-item>
              <el-button @click="search">{{ $t("query") }}</el-button>
            </el-form-item>
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
          </el-form-item>
        </el-form>
        <div v-if="state.dataList && state.dataList.length > 0">
          <!-- 固定表头并固定监测点位列 -->
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column width="220" prop="monitoringPoint" label="监测点位" header-align="center" align="center" fixed="left"></el-table-column>
            <el-table-column prop="year" label="年" header-align="center" align="center"></el-table-column>
            <el-table-column prop="month" label="月" header-align="center" align="center"></el-table-column>
            <el-table-column prop="day" label="日" header-align="center" align="center"></el-table-column>
            <el-table-column prop="transparency" label="透明度(cm)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="dissolvedOxygen" label="溶解氧(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="waterTemperature" label="水温(℃)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ph" label="pH值" header-align="center" align="center"></el-table-column>
            <el-table-column prop="totalNitrogen" label="总氮(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ammoniaNitrogen" label="氨氮(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="totalPhosphorus" label="总磷(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="chlorophyllA" label="叶绿素a(μg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="chemicalOxygenDemand" label="化学需氧量(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="nitriteNitrogen" label="亚硝酸盐氮(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column prop="nitrateNitrogen" label="硝酸盐氮(mg/L)" header-align="center" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        </div>
        <el-container style="justify-content: center" v-if="state.dataList && state.dataList.length === 0"> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
      </el-main>
    </div>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, nextTick, onBeforeUnmount } from "vue";
import AddOrUpdate from "./interface-data-shuiku-add-or-update.vue";
import { ElLoading } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
const { $t } = globalLanguage();
const route = useRoute();
const router = useRouter();

interface Tree {
  label: string;
  children?: Tree[];
}

const handleNodeClick = (data: { label: string }, getNode: { parent: { label: string, parent?: { label: string } }, data: { children: any[] } }) => {
  const month = getNode.parent.label;
  const year = month ? getNode.parent.parent?.label : undefined;

  if (getNode.data.children.length === 0) {
    if (year === undefined && month === undefined) {
      state.dataForm.year = data.label;
      state.getDataList();
      return;
    }
    if (year === undefined) {
      state.dataForm.year = month;
      state.dataForm.month = data.label;
      state.getDataList();
      return;
    }
    state.dataForm.year = year;
    state.dataForm.month = month;
    state.dataForm.monitoringPoint = data.label;
    state.getDataList();
  }
};

const treeData = reactive<Tree[]>([]);

const getTreeData = () => {
  baseService.get("/task/conditionshuiku/list", { limit: 100000 }).then((res) => {
    const data = res.data.map((item: any) => ({
      label: item.label,
      children: item.children || []
    }));
    treeData.splice(0, treeData.length);
    treeData.push(...data);
  });
};

getTreeData();

const defaultProps = {
  children: "children",
  label: "label"
};

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/interfaceDataShuiku/page",
  getDataListIsPage: true,
  exportURL: "/task/interfaceDataShuiku/export",
  deleteURL: "/task/interfaceDataShuiku",
  uploadURL: "/task/interfaceDataShuiku/upload",
  dataForm: {
    year: "",
    month: "",
    monitoringPoint: "",
    startTime: "",
    endTime: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const tableHeight = ref<number>(400);
const calculateTableHeight = () => {
  const offset = 260;
  const h = window.innerHeight - offset;
  tableHeight.value = h > 200 ? h : 200;
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onMounted(() => {
  if (route.query.monitoringPoint) {
    state.dataForm.monitoringPoint = route.query.monitoringPoint as string;
    if (route.query.from === 'monitoring') {
      nextTick(() => {
        state.getDataList();
      });
    }
  }
});

const search = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

const state_excel = reactive({
  selectedFile: null
});

function handleFileChange(file: any, fileList: any) {
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw;
  } else {
    state_excel.selectedFile = null;
  }
}

function handleFileUpload() {
  if (!state_excel.selectedFile) {
    ElMessage.error("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  openLoading();
  baseService
    .post("/task/interfaceDataShuiku/excel-input", formData)
    .then((response) => {
      ElMessage.success("上传成功");
      state.getDataList();
      closeLoading();
      getTreeData();
      state_excel.selectedFile = null;
      updateBgaDataRef.value.fileList = [];
    })
    .catch((error) => {
      console.error("错误:", error);
      ElMessage.success("上传成功");
      state.getDataList();
      closeLoading();
      getTreeData();
      state_excel.selectedFile = null;
      updateBgaDataRef.value.fileList = [];
    });
}

const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};

const closeLoading = () => {
  ElLoading.service().close();
};

const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/InterfaceDataShuikuExcelDataImport.xlsx";
  downloadLink.download = "水质水库数据Excel模板.xlsx";
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};
</script>

<style scoped>
.el-radio-button {
  --el-radio-button-checked-bg-color: #edf9f8;
  --el-radio-button-checked-text-color: #17b3a3;
}
::v-deep .month-card .el-radio-button__inner {
  width: 300px;
  height: 60px;
  font-size: 18px;
  padding-top: 20px;
}
</style>