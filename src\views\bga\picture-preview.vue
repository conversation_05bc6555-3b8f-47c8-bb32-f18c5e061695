<template>
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select clearable v-model="state.dataForm.address" value-key="id" :placeholder="$t('bga.address')" @change="onSelectAddress" @clear="clearAddress">
          <el-option v-for="item in addressListRef" :key="item.id" :label="item.address" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="state.dataForm.createDate" format="YYYY-MM-DD" type="date" :placeholder="$t('bga.selectDateTime')" />
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column prop="url" width="550" :label="$t('bga.originalImage')" header-align="center" align="center">
        <template #default="scope">
          <el-image preview-teleported style="width: 500px; height: 300px" :src="scope.row.url" :preview-src-list="[scope.row.url]" lazy fit="fill"></el-image>
        </template>
      </el-table-column>
      <el-table-column  prop="area" :label="$t('bga.segmentationRatio')" header-align="center" align="center" width="150">
        <template #default="scope">
          <el-tag :key="scope.row.id" effect="dark">
            {{ scope.row.area + "%" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="urlRes" :label="$t('bga.segmentedResultImage')" header-align="center" align="center" width="550">
        <template #default="scope">
          <div v-if="scope.row.urlRes">
            <el-image preview-teleported style="width: 500px; height: 300px" :src="scope.row.urlRes" fit="fill" lazy :preview-src-list="[scope.row.urlRes]"></el-image>
          </div>
          <div v-else>
            <el-button type="primary" v-if="!buttonLoading[scope.row.id]" @click="detectionImg(scope.row.id)">{{ $t("bga.annotate") }}</el-button>
            <el-button type="primary" v-else :loading="buttonLoading[scope.row.id]">{{ $t("bga.Loadding") }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="100">
        <template #default="scope">
          <el-button type="primary" link @click="showDetails(scope.row.id)">{{ $t("bga.comparePreview") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <show-picture-preview ref="showPictureId"></show-picture-preview>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, Ref, onMounted } from "vue";
import baseService from "@/service/baseService";
import emits from "@/utils/emits";
import { EMitt } from "@/constants/enum";
import { AddressObject } from "@/types/interface";
import showPicturePreview from "./show-picture-preview.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/oss/files/page",
  getDataListIsPage: true,
  exportURL: "/oss/files/export",
  deleteURL: "/oss/files",
  deleteIsBatch: true,
  orderField: "create_date",
  dataForm: {
    fileName: "",
    address: "",
    addressId: "",
    createDate: ""
  }
});
onMounted(() => {
  getAddressList("/bga/taddress/page", 1);
});
const state = reactive({ ...useView(view), ...toRefs(view) });
const showPictureId = ref();
// 参看详情
function showDetails(id: string) {
  showPictureId.value.init(id);
}
const buttonLoading: Ref<{ [key: string]: boolean }> = ref({});
// 刷新页面
const onRefresh = () => {
  emits.emit(EMitt.OnReloadTabPage);
};
// 标注图片
function detectionImg(id: string) {
  buttonLoading.value[id] = true;
  baseService
    .post(`/bga/common/detection/${id}`)
    .then(({ data: res }) => {
      if (res.code !== 0) {
        buttonLoading.value[id] = false;
        return ElMessage({
          message: $t("prompt.failed"),
          type: "error"
        });
      }
      if (res.data) {
        let it = state.dataList?.find((item) => {
          return item.id === id;
        });
        if (it) {
          it.urlRes = res.data.urlRes;
          it.area = res.data.area;
        }
        buttonLoading.value[id] = false;
      }
      ElMessage({
        type: "success",
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          onRefresh();
        }
      });
    })
    .catch(() => {});
}
const addressListRef = ref<AddressObject[]>([]);
// 获取地址列表
async function getAddressList(url: string, type: number) {
  const { data } = await baseService.get(url, {
    page: 1,
    limit: 99999
  });
  if (data.list.length > 0) {
    addressListRef.value = data.list;
  }
}
// 选择地址
function onSelectAddress(value: string) {
  let it = addressListRef.value.find((item) => {
    return item.id === value;
  });
  if (it?.address) {
    state.dataForm.address = it.address;
    state.dataForm.addressId = it.id;
  }
}
// 清除地址状态
function clearAddress() {
  state.dataForm.address = "";
  state.dataForm.addressId = "";
}
</script>
