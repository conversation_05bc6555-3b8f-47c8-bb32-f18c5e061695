<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      
      <el-form-item :label="$t('monitoring.stcd')" prop="stcd">
        <el-input v-model="dataForm.stcd" :placeholder="$t('monitoring.stcd')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.stnm')" prop="stnm">
        <el-input v-model="dataForm.stnm" :placeholder="$t('monitoring.stnm')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('所属流域')" prop="location">
        <el-input v-model="dataForm.location" :placeholder="$t('所属流域')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.eslo')" prop="eslo">
        <el-input v-model="dataForm.eslo" :placeholder="$t('monitoring.eslo')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.ntla')" prop="ntla">
        <el-input v-model="dataForm.ntla" :placeholder="$t('monitoring.ntla')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.stlvl')" prop="stlvl">
        <el-input v-model="dataForm.stlvl" :placeholder="$t('monitoring.stlvl')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.bnnm')" prop="bnnm">
        <el-input v-model="dataForm.bnnm" :placeholder="$t('monitoring.bnnm')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.rvnm')" prop="rvnm">
        <el-input v-model="dataForm.rvnm" :placeholder="$t('monitoring.rvnm')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.staddr')" prop="staddr">
        <el-input v-model="dataForm.staddr" :placeholder="$t('monitoring.staddr')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.adcd')" prop="adcd">
        <el-input v-model="dataForm.adcd" :placeholder="$t('monitoring.adcd')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.wudcd')" prop="wudcd">
        <el-input v-model="dataForm.wudcd" :placeholder="$t('monitoring.wudcd')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.munit')" prop="munit">
        <el-input v-model="dataForm.munit" :placeholder="$t('monitoring.munit')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.msunit')" prop="msunit">
        <el-input v-model="dataForm.msunit" :placeholder="$t('monitoring.msunit')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.mnfrq')" prop="mnfrq">
        <el-input v-model="dataForm.mnfrq" :placeholder="$t('monitoring.mnfrq')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.atst')" prop="atst">
        <el-input v-model="dataForm.atst" :placeholder="$t('是或否')" clearable></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.fndym')" prop="fndym">
        <el-input v-model="dataForm.fndym" :placeholder="$t('monitoring.fndym')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.endym')" prop="endym">
        <el-input v-model="dataForm.endym" :placeholder="$t('monitoring.endym')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('monitoring.remarks')" prop="remarks">
        <el-input v-model="dataForm.remarks" :placeholder="$t('monitoring.remarks')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  const emit = defineEmits(["refreshDataList"]);
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: "",
    wystcd: "",
    stcd: "",
    stnm: "",
    location:"",
    stlvl: "",
    bnnm: "",
    rvnm: "",
    eslo: "",
    ntla: "",
    staddr: "",
    adcd: "",
    wudcd: "",
    munit: "",
    msunit: "",
    mnfrq: "",
    atst: "",
    fndym: "",
    endym: "",
    remarks: ""
  });

  const rules = ref({

    stcd: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    stnm: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    eslo: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    ntla: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
  });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/bga/monitoringszjhcstation/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };
  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/bga/monitoringszjhcstation", dataForm).then((res) => {
        ElMessage.success({
          message: $t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
