<template>
  <div class="mod-bga__phytophylum">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-form-item>
          <el-input style="width: 200px" v-model="state.dataForm.nameCn" :placeholder="$t('phytoplankto.chineseName')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="state.dataForm.createDate" format="YYYY-MM-DD" type="date" :placeholder="$t('bga.selectDateTime')" />
        </el-form-item>
        <el-form-item>
          <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
        </el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="state.dataListSelectionChangeHandle"
    >
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column
        prop="nameCn"
        :label="$t('phytoplankto.chineseName')"
        header-align="center"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="nameLatin"
        :label="$t('phytoplankto.latinName')"
        header-align="center"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="remark"
        :label="$t('备注')"
        header-align="center"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createdAt"
        :label="$t('bga.createDate')"
        header-align="center"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="state.limit"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="state.pageSizeChangeHandle"
      @current-change="state.pageCurrentChangeHandle"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./phytophylum-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();

  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/phytophylum/page",
    getDataListIsPage: true,
    exportURL: "/bga/phytophylum/export",
    deleteURL: "/bga/phytophylum",
    dataForm: {
      nameCn: "",
      createDate: ""
    }
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
</script>

<style scoped>
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>

<style>
  .avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  .avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 100px;
    text-align: center;
  }
</style>
