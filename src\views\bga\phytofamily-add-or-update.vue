<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('phytoplankto.chineseName')" prop="nameCn">
        <el-input v-model="dataForm.nameCn" :placeholder="$t('phytoplankto.chineseName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.latinName')" prop="nameLatin">
        <el-input v-model="dataForm.nameLatin" :placeholder="$t('phytoplankto.latinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('bga.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  nameCn: "",
  nameLatin: "",
  remark: ""
});

const rules = ref({
  nameCn: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nameEn: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nameLatin: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/bga/phytofamily/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/bga/phytofamily", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
