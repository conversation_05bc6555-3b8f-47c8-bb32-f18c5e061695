<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('weather.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('weather.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.windSpeed')" prop="windSpeed">
        <el-input v-model="dataForm.windSpeed" :placeholder="$t('weather.windSpeed')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.windDirection')" prop="windDirection">
        <el-input v-model="dataForm.windDirection" :placeholder="$t('weather.windDirection')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.temperature')" prop="temperature">
        <el-input v-model="dataForm.temperature" :placeholder="$t('weather.temperature')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.atmosphericPressure')" prop="atmosphericPressure">
        <el-input v-model="dataForm.atmosphericPressure" :placeholder="$t('weather.atmosphericPressure')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.rainfall')" prop="rainfall">
        <el-input v-model="dataForm.rainfall" :placeholder="$t('weather.rainfall')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.par')" prop="par">
        <el-input v-model="dataForm.par" :placeholder="$t('weather.par')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.relativeHumidity')" prop="relativeHumidity">
        <el-input v-model="dataForm.relativeHumidity" :placeholder="$t('weather.relativeHumidity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.solarRadiation')" prop="solarRadiation">
        <el-input v-model="dataForm.solarRadiation" :placeholder="$t('weather.solarRadiation')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('weather.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('weather.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('weather.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationCode: "",
  createdAt: "",
  windSpeed: "",
  windDirection: "",
  temperature: "",
  atmosphericPressure: "",
  rainfall: "",
  par: "",
  relativeHumidity: "",
  solarRadiation: "",
  remark: ""
});

const rules = ref({
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  windSpeed: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  windDirection: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  temperature: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  atmosphericPressure: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  rainfall: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  par: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  relativeHumidity: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  solarRadiation: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/weather/weatherinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/weather/weatherinfo", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
