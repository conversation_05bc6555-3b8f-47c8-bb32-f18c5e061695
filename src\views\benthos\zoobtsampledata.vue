<template>
    <div class="mod-bga__phytosampledata">
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="uploadBgaDataHandle()">{{ $t('benthos.uploadDataTable') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="stationId" :label="$t('benthos.samplingId')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="samplingLocation" :label="$t('benthos.samplingLocation')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="sampleVolumeLiters" :label="$t('benthos.sampleVolumeLiters')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="concentratedVolume" :label="$t('benthos.concentratedVolume')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="dilutionFactor" :label="$t('benthos.dilutionFactor')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="fieldOfViewCount" :label="$t('benthos.fieldOfViewCount')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="fieldArea" :label="$t('benthos.fieldArea')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="countingArea" :label="$t('benthos.countingArea')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="countingFrameVolume" :label="$t('benthos.countingFrameVolume')" header-align="center" align="center"></el-table-column>
        <el-table-column width="150" prop="countingFrameArea" :label="$t('benthos.countingFrameArea')" header-align="center" align="center"></el-table-column>
        <el-table-column width="160" prop="samplingAt" :label="$t('benthos.samplingAt')" header-align="center" align="center"></el-table-column>
        <el-table-column width="160" prop="createdAt" :label="$t('benthos.createdAt')" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template v-slot="scope">
            <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
            <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
      <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
    </div>
  </template>
  
  <script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./zoobtsampledata-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/benthos/zoobtsampledata/page",
    getDataListIsPage: true,
    exportURL: "/benthos/zoobtsampledata/export",
    deleteURL: "/benthos/zoobtsampledata",
    uploadURL: "/benthos/zoobtsampledata/upload"
  });
  
  const state = reactive({ ...useView(view), ...toRefs(view) });
  
  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };
  </script>
  