<template>
  <div class="mod-bga__phytoSampleStatistics">
    <el-container class="layout-container-demo" style="height: 800px" v-loading="sampling.dataListLoading">
      <el-aside width="500px">
        <el-scrollbar>
          <el-form :inline="true" :model="sampling.dataForm" @keyup.enter="sampling.getDataList()">
            <el-form-item>
              <el-input style="width: 300px" v-model="sampling.dataForm.samplingLocation" :placeholder="$t('phytoplankto.samplingLocation')" clearable></el-input>
            </el-form-item>

<!--            <el-form-item>-->
<!--              <el-date-picker style="width: 300px" v-model="sampling.dataForm.samplingAt" format="YYYY-MM-DD" type="date" :placeholder="$t('phytoplankto.samplingAt')" />-->
<!--            </el-form-item>-->

            <div class="container">
<!--              <div class="block">-->
<!--                <span class="demonstration"></span>-->
<!--                <el-date-picker-->
<!--                  v-model="sampling.dataForm.createAt"-->
<!--                  type="year"-->
<!--                  placeholder="选择年">-->
<!--                </el-date-picker>-->
<!--              </div>-->
<!--              <div class="block">-->
<!--                <span class="demonstration"></span>-->
<!--                <el-date-picker-->
<!--                  v-model="sampling.dataForm.createAt"-->
<!--                  type="month"-->
<!--                  placeholder="选择月">-->
<!--                </el-date-picker>-->
<!--              </div>-->
              <div class="block">
                <span class="demonstration"></span>
                <el-date-picker
                  v-model="sampling.dataForm.createdAt"
                  type="date"
                  value-format='YYYY-MM-DD'
                  placeholder="创建时间">
                </el-date-picker>
              </div>
            </div>
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <el-button @click="sampling.getDataList()">{{ $t("query") }}</el-button>
              <el-button type="primary" @click="addOrUpdateHandleData()">{{ $t("add") }}</el-button>
            </div>
          </el-form>
          <div class="scrollbar" style="width: 100%">
            <el-card :class="{ active: selectSample === item.stationId }" v-for="item in sampling.dataList" @click.stop="getSamplingInfo(item)" shadow="hover" :key="+item.id" :body-style="{ padding: '20px 0', width: '100%' }">
              <div class="card-header">
                <span>{{ $t("phytoplankto.sampleDataId") }}: {{ item.stationId }} - {{ item.samplingLocation }}</span>
                <div class="card-button">
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'detail')">{{ $t("details") }}</el-button>
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'update')">{{ $t("update") }}</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </el-scrollbar>
      </el-aside>
      <el-container v-if="samplingInfo.id">
        <el-header height="35%">
          <el-descriptions :title="$t('phytoplankto.sampleDataInfo')">
            <el-descriptions-item :label="$t('phytoplankto.samplingLocation')">
              <el-tag> {{ samplingInfo.samplingLocation }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.sampleVolumeLiters')">{{ samplingInfo.sampleVolumeLiters }}(L)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.concentratedVolume')">{{ samplingInfo.concentratedVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.dilutionFactor')">{{ samplingInfo.dilutionFactor }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.fieldOfViewCount')">{{ samplingInfo.fieldOfViewCount ? samplingInfo.fieldOfViewCount + "(个)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.fieldArea')">{{ samplingInfo.fieldArea ? samplingInfo.fieldArea + "(mm2)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingArea')">{{ samplingInfo.countingArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingFrameVolume')">{{ samplingInfo.countingFrameVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingFrameArea')">{{ samplingInfo.countingFrameArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.createDate')">{{ samplingInfo.createdAt }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.samplingAt')">
              <el-tag type="success"> {{ samplingInfo.samplingAt }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.dataSource')">
              <el-tag type="warning"> {{ samplingInfo.dataSource }} </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-header>
        <el-main>
          <el-form :inline="true" :model="samplingTestData.dataForm" @keyup.enter="samplingTestData.getDataList()">
            <el-form-item>
              <el-form-item>
                <el-input style="width: 200px" v-model="samplingTestData.dataForm.species" :placeholder="$t('phytoplankto.species')" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-button @click="samplingTestData.getDataList()">{{ $t("query") }}</el-button>
              </el-form-item>
              <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="samplingTestData.deleteHandle()">{{ $t("delete") }}</el-button>
            </el-form-item>
          </el-form>
          <el-table v-loading="samplingTestData.dataListLoading" :data="samplingTestData.dataList" border @selection-change="samplingTestData.dataListSelectionChangeHandle" style="width: 100%">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingId" :label="$t('phytoplankto.sampleDataId')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingLocation" :label="$t('phytoplankto.samplingLocation')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="category" :label="$t('phytoplankto.category')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="species" :label="$t('phytoplankto.species')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="latinName" :label="$t('phytoplankto.latinName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="wetWeight" :label="$t('phytoplankto.wetWeight')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="count" :label="$t('phytoplankto.count')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="cellCount" :label="$t('phytoplankto.cellCount')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="biomass" :label="$t('phytoplankto.biomass')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="createdAt" :label="$t('bga.createDate')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="updateAt" :label="$t('bga.updateDate')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="deleteAt" :label="$t('bga.deleteDate')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="samplingAt" :label="$t('bga.samplingAt')" header-align="center" align="center"></el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="samplingTestData.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="samplingTestData.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="samplingTestData.limit"
            :total="samplingTestData.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="samplingTestData.pageSizeChangeHandle"
            @current-change="samplingTestData.pageCurrentChangeHandle"
          ></el-pagination>
        </el-main>
      </el-container>
      <el-container style="justify-content: center" v-else> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
    </el-container>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="samplingTestData.getDataList"></add-or-update>
    <show-or-update ref="showOrUpdateRef" @refreshDataList="sampling.getDataList"></show-or-update>
    <add-or-update-sample ref="addOrUpdateSampleRef" @refreshDataList="sampling.getDataList"></add-or-update-sample>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onMounted, reactive, ref, toRefs } from "vue";
import ShowOrUpdate from "./sampling-show-or-update.vue";
import AddOrUpdate from "./phytodata-add-or-update.vue";
import AddOrUpdateSample from "./phytosampledata-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import baseService from "@/service/baseService";
const { $t } = globalLanguage();
const view1 = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/phytosampledata/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytosampledata/export",
  deleteURL: "/bga/phytosampledata",
  dataForm: {
    id: "",
    samplingLocation: "",
    createdAt: "",
    samplingAt:""
  }
});
const view2 = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/phytodata/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytodata/export",
  deleteURL: "/bga/phytodata",
  dataForm: {
    samplingId: 0,
    species: ""
  }
});
onMounted(() => {
  baseService.get("/bga/phytosampledata/page").then(({ data }) => {
    const firstData = data.list.filter((item: any) => item.stationId === selectSample.value);
    Object.assign(samplingInfo.value, firstData[0]);
    view2.dataForm.samplingId = selectSample.value;
    samplingTestData.getDataList();
  });
});
const samplingTestData = reactive({ ...useView(view2), ...toRefs(view2) });
const samplingInfo = ref({
  id: "",
  stationId: "",
  samplingLocation: "",
  sampleVolumeLiters: "",
  concentratedVolume: "",
  dilutionFactor: "",
  fieldOfViewCount: "",
  fieldArea: "",
  countingArea: "",
  countingFrameVolume: "",
  countingFrameArea: "",
  samplingAt: "",
  createdAt: "",
  updateAt: "",
  deleteAt: "",
  dataSource: ""
});
const sampling = reactive({ ...useView(view1), ...toRefs(view1) });
const selectSample = ref(1);
const getSamplingInfo = (info: any) => {
  selectSample.value = info.stationId;
  Object.assign(samplingInfo.value, info);
  view2.dataForm.samplingId = info.stationId;
  samplingTestData.getDataList();
};
const showOrUpdateRef = ref();
const showOrUpdateHandle = (id: any, type: string) => {
  showOrUpdateRef.value.init(id, type);
};
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const addOrUpdateSampleRef = ref();
const addOrUpdateHandleData = (id?: number) => {
  addOrUpdateSampleRef.value.init(id);
};
</script>

<style scoped lang="less">
@import "../../assets/theme/base.less";
.card-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.scrollbar {
  height: 3000px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.el-scrollbar__view .el-card:hover {
  color: @light-text-active;
}

.el-scrollbar__view .el-card.active {
  background: @light-bg-active;
  border: 1px solid @light-text-active;
  color: @light-text-active;
}
.el-card {
  cursor: pointer;
  border-radius: 16px;
  width: 100%;
  margin-top: 5%;
}
</style>
