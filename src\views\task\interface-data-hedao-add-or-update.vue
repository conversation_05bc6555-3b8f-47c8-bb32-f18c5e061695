<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="监测站点名称" prop="station">
        <el-input v-model="dataForm.station" placeholder="监测站点名称"></el-input>
      </el-form-item>
      <el-form-item label="年" prop="year">
        <el-input v-model="dataForm.year" placeholder="年"></el-input>
      </el-form-item>
      <el-form-item label="月" prop="month">
        <el-input v-model="dataForm.month" placeholder="月"></el-input>
      </el-form-item>
      <el-form-item label="日" prop="day">
        <el-input v-model="dataForm.day" placeholder="日"></el-input>
      </el-form-item>
      <el-form-item label="透明度" prop="transparency">
        <el-input v-model="dataForm.transparency" placeholder="透明度"></el-input>
      </el-form-item>
      <el-form-item label="溶解氧" prop="dissolved_oxygen">
        <el-input v-model="dataForm.dissolved_oxygen" placeholder="溶解氧"></el-input>
      </el-form-item>
      <el-form-item label="水温" prop="water_temperature">
        <el-input v-model="dataForm.water_temperature" placeholder="水温"></el-input>
      </el-form-item>
      <el-form-item label="PH" prop="ph">
        <el-input v-model="dataForm.ph" placeholder="PH"></el-input>
      </el-form-item>
      <el-form-item label="总氮" prop="total_nitrogen">
        <el-input v-model="dataForm.total_nitrogen" placeholder="总氮"></el-input>
      </el-form-item>
      <el-form-item label="氨氮" prop="ammonia_nitrogen">
        <el-input v-model="dataForm.ammonia_nitrogen" placeholder="氨氮"></el-input>
      </el-form-item>
      <el-form-item label="总磷" prop="total_phosphorus">
        <el-input v-model="dataForm.total_phosphorus" placeholder="总磷"></el-input>
      </el-form-item>
      <el-form-item label="叶绿素a" prop="chlorophyll_a">
        <el-input v-model="dataForm.chlorophyll_a" placeholder="叶绿素a"></el-input>
      </el-form-item>
      <el-form-item label="化学需氧量" prop="chemical_oxygen_demand">
        <el-input v-model="dataForm.chemical_oxygen_demand" placeholder="化学需氧量"></el-input>
      </el-form-item>
      <el-form-item label="亚硝酸盐氮" prop="nitrite_nitrogen">
        <el-input v-model="dataForm.nitrite_nitrogen" placeholder="亚硝酸盐氮"></el-input>
      </el-form-item>
      <el-form-item label="硝酸盐氮" prop="orthophosphate">
        <el-input v-model="dataForm.orthophosphate" placeholder="硝酸盐氮"></el-input>
      </el-form-item>
      <el-form-item label="水深" prop="high">
        <el-input v-model="dataForm.high" placeholder="水深"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  const emit = defineEmits(["refreshDataList"]);

  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: '', station: '', year: '',  month: '', day: '', transparency: '', dissolved_oxygen: '', water_temperature:'', ph:'',total_nitrogen:'',ammonia_nitrogen:'',total_phosphorus:'',chlorophyll_a:'',chemical_oxygen_demand:'',nitrite_nitrogen:'',orthophosphate:'',high:''});
    const rules = ref({
            year: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            month: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ]

    });



  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/task/interfaceDatahedao/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/task/interfaceDatahedao", dataForm).then((res) => {
        ElMessage.success({
          message: '成功',
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
