<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('benthos.zoobtId')" prop="zoobtId">
        <el-input :placeholder="$t('benthos.zoobtId')" v-model="dataForm.zoobtId"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtName')" prop="zoobtName">
        <el-input :placeholder="$t('benthos.zoobtName')" v-model="dataForm.zoobtName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.stationCode')" prop="stationCode">
        <el-input :placeholder="$t('benthos.stationCode')" v-model="dataForm.stationCode"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.stationName')" prop="stationName">
        <el-input :placeholder="$t('benthos.stationName')" v-model="dataForm.stationName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.averageBiomass')" prop="averageBiomass">
        <el-input :placeholder="$t('benthos.averageBiomass')" v-model="dataForm.averageBiomass"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.createdAt')" prop="createdAt">
        <el-input :placeholder="$t('benthos.createdAt')" v-model="dataForm.createdAt"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.updateAt')" prop="updateAt">
        <el-input :placeholder="$t('benthos.updateAt')" v-model="dataForm.updateAt"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.deleteAt')" prop="deleteAt">
        <el-input :placeholder="$t('benthos.deleteAt')" v-model="dataForm.deleteAt"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.remark')" prop="remark">
        <el-input :placeholder="$t('benthos.remark')" v-model="dataForm.remark"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  zoobtId: "",
  zoobtName: "",
  stationCode: "",
  stationName: "",
  averageBiomass: "",
  createdAt: "",
  updateAt: "",
  deleteAt: "",
  remark: ""
});

const rules = ref({
  zoobtId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  averageBiomass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  updateAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  deleteAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/benthos/zoobtaveragebiomass/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/benthos/zoobtaveragebiomass", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
