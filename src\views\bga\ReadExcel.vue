<script setup lang="ts">
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./phytosampledata-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";

import axios from 'axios';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/phytosampledata/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytosampledata/export",
  deleteURL: "/bga/phytosampledata",
  uploadURL: "/bga/phytosampledata/upload",
  ExcelURL: "/bga/phytosampledata/excel"
});

//const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};





const state = reactive<any>({
  selectedFile: null
});

function handleFileChange(event : any) {
  // 当用户选择文件时，更新selectedFile
  state.selectedFile = event.target.files[0];
}

function handleFileUpload() {
  const formData = new FormData();
  formData.append("file", state.selectedFile);

  baseService
    .post("/bga/excel/excel-input", formData)
    .then((response) => {
      console.log("成功:", response.data);
    })
    .catch((error) => {
      console.error("错误:", error);
    });
}
</script>

<template>
<!--  <div class="mod-bga__excel">-->
<!--    <el-upload>-->
<!--      action="bga/excel" class="upload-demo" name="file" :headers="headers" :limit="1" ref="handleFileUpload" accept=".xlsx,.xls,.xlsm" :data="{metaid:this.$route.meta.routerId}" drag>-->
<!--      <i class="el-icon-upload"></i>-->
<!--      <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>-->
<!--      <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件，且不超过10MB</div>-->
<!--    </el-upload>-->
<!--  </div>-->
  <div class="mod-bga__excel">
    <input type="file" id="ExcelInput" @change="handleFileChange" accept=".xlsx,.xls,.xlsm"/>
    <button @click="handleFileUpload">上传文件</button>
  </div>
</template>

<style scoped lang="less">

</style>
