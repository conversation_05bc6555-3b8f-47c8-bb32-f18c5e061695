<script setup>
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  fileName: "",
  fileId: "",
  url: "",
  urlRes: null,
  memo: null,
  addressId: "",
  address: "",
  area: 0.0,
  creator: null,
  createDate: ""
});
const init = (id) => {
  dataForm.id = "";
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  if (id) {
    getPictureDetails(id);
  }
};
function getPictureDetails(id) {
  baseService.get(`/oss/files/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
}
defineExpose({
  init
});
</script>

<template>
  <el-dialog align-center v-model="visible" :title="$t('details')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" label-postion="left" label-width="auto" size="large" ref="dataFormRef">
      <el-form-item :label="$t('bga.fileName')">
        <el-input v-model="dataForm.fileName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.fileId')">
        <el-input v-model="dataForm.fileId"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.originalImage')">
        <el-image :preview-src-list="[dataForm.url]" style="width: 200px; height: 100px" :src="dataForm.url" fit="contain"></el-image>
      </el-form-item>
      <el-form-item :label="$t('bga.segmentedResultImage')" label-width="110px">
        <el-image :preview-src-list="[dataForm.urlRes]" style="width: 200px; height: 100px" :src="dataForm.urlRes" fit="contain"></el-image>
      </el-form-item>
      <el-form-item :label="$t('bga.segmentationRatio')">
        <el-tag :key="dataForm.id"  effect="dark">
          {{ dataForm.area + "%" }}
        </el-tag>
      </el-form-item>
      <el-form-item :label="$t('bga.createDate')" header-align="center" align="center">
        <el-input v-model="dataForm.createDate"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.address')" header-align="center" align="center">
        <el-input v-model="dataForm.address"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.remark')" header-align="center" align="center">
        <el-input v-model="dataForm.memo"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
