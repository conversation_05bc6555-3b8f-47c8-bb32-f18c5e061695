<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('dbk.monitorTime')" prop="monitorTime">
        <el-input v-model="dataForm.monitorTime" :placeholder="$t('dbk.monitorTime')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.windSpeed')" prop="windSpeed">
        <el-input v-model="dataForm.windSpeed" :placeholder="$t('dbk.windSpeed')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.accumulatedRainfall')" prop="accumulatedRainfall">
        <el-input v-model="dataForm.accumulatedRainfall" :placeholder="$t('dbk.accumulatedRainfall')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.atmosphericTemperature')" prop="atmosphericTemperature">
        <el-input v-model="dataForm.atmosphericTemperature" :placeholder="$t('dbk.atmosphericTemperature')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.digitalAirPressure')" prop="digitalAirPressure">
        <el-input v-model="dataForm.digitalAirPressure" :placeholder="$t('dbk.digitalAirPressure')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.liquidLevel')" prop="liquidLevel">
        <el-input v-model="dataForm.liquidLevel" :placeholder="$t('dbk.liquidLevel')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.evaporation')" prop="evaporation">
        <el-input v-model="dataForm.evaporation" :placeholder="$t('dbk.evaporation')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.windDirection')" prop="windDirection">
        <el-input v-model="dataForm.windDirection" :placeholder="$t('dbk.windDirection')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.atmosphericHumidity')" prop="atmosphericHumidity">
        <el-input v-model="dataForm.atmosphericHumidity" :placeholder="$t('dbk.atmosphericHumidity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.tbqTotalRadiation')" prop="tbqTotalRadiation">
        <el-input v-model="dataForm.tbqTotalRadiation" :placeholder="$t('dbk.tbqTotalRadiation')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.lightIntensity')" prop="lightIntensity">
        <el-input v-model="dataForm.lightIntensity" :placeholder="$t('dbk.lightIntensity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('dbk.cumulativeRadiation')" prop="cumulativeRadiation">
        <el-input v-model="dataForm.cumulativeRadiation" :placeholder="$t('dbk.cumulativeRadiation')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  monitorTime: "",
  windSpeed: "",
  accumulatedRainfall: "",
  atmosphericTemperature: "",
  digitalAirPressure: "",
  liquidLevel: "",
  evaporation: "",
  windDirection: "",
  atmosphericHumidity: "",
  tbqTotalRadiation: "",
  lightIntensity: "",
  cumulativeRadiation: "",
});

const rules = ref({
  monitorTime: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  windSpeed: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  accumulatedRainfall: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  atmosphericTemperature: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  digitalAirPressure: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  liquidLevel: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  evaporation: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  windDirection: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  atmosphericHumidity: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  tbqTotalRadiation: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  lightIntensity: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  cumulativeRadiation: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/task/interface_data_dbk/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/task/interface_data_dbk", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
