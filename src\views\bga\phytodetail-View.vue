<template>
  <el-dialog v-model="visible" align-center :title="$t('查看详细信息')" :close-on-click-modal="false" :close-on-press-escape="false" width="80%">
    <el-form :model="dataForm" label-width="auto">
      <el-form-item :label="$t('phytoplankto.name')">
        <el-input v-model="dataForm.name" :placeholder="$t('phytoplankto.name')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.phylumName')">
        <el-select v-model="dataForm.phylumName" :placeholder="$t('phytoplankto.phylumName')" disabled>
          <el-option v-for="item in phytophylumData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.className')">
        <el-select v-model="dataForm.className" :placeholder="$t('phytoplankto.className')" disabled>
          <el-option v-for="item in phytoclassData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameOrder')">
        <el-select v-model="dataForm.nameOrder" :placeholder="$t('phytoplankto.nameOrder')" disabled>
          <el-option v-for="item in phytoorderData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameFamily')">
        <el-select v-model="dataForm.nameFamily" :placeholder="$t('phytoplankto.nameFamily')" disabled>
          <el-option v-for="item in phytofamilyData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameGenus')">
        <el-select v-model="dataForm.nameGenus" :placeholder="$t('phytoplankto.nameGenus')" disabled>
          <el-option v-for="item in phytogenusData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.type')">
        <el-input v-model="dataForm.type" :placeholder="$t('phytoplankto.type')" readonly></el-input>
      </el-form-item>

      <el-form-item :label="$t('phytoplankto.identification')">
        <el-input v-model="dataForm.identification" :placeholder="$t('phytoplankto.identification')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.stationAddr')">
        <el-input v-model="dataForm.stationAddr" :placeholder="$t('phytoplankto.stationAddr')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.autecology')">
        <el-input v-model="dataForm.autecology" :placeholder="$t('phytoplankto.autecology')" readonly></el-input>
      </el-form-item>
      <el-form-item label="浮游植物图片">
        <div v-if="dataForm.img" class="image-container" @click="showImagePreview">
          <img
            :src="dataForm.img"
            alt="预览图片"
            style="max-width: 300px; max-height: 300px; margin-top: 10px; cursor: pointer; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);"
          />
        </div>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.contributor')">
        <el-input v-model="dataForm.contributor" :placeholder="$t('phytoplankto.contributor')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.reviewer')">
        <el-input v-model="dataForm.reviewer" :placeholder="$t('phytoplankto.reviewer')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.status')">
        <el-select v-model="dataForm.status" :placeholder="$t('phytoplankto.status')" disabled>
          <el-option v-for="item in stateStatus" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.description')">
        <el-input v-model="dataForm.description" :placeholder="$t('phytoplankto.description')" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.remark')">
        <el-input v-model="dataForm.remark" :placeholder="$t('bga.remark')" readonly></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
    </template>
  </el-dialog>

  <!-- 图片预览 -->
  <div v-if="previewVisible" class="image-preview" @click="closePreview">
    <div class="preview-container">
      <img
        ref="previewImage"
        :src="dataForm.img"
        alt="预览图片"
        @wheel.prevent="handleZoom"
        @mousedown="startDrag"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";

  const { $t } = globalLanguage();
  const visible = ref(false);

  const dataForm = reactive({
    id: "",
    name: "",
    phylumName: "",
    className: "",
    nameOrder: "",
    nameFamily: "",
    nameGenus: "",
    type: "",
    identification: "",
    stationAddr: "",
    autecology: "",
    contributor: "",
    reviewer: "",
    status: "",
    description: "",
    remark: "",
    img: ""
  });

  const params = ref({
    page: 1,
    limit: 99999
  });
  // 数据状态
  const stateStatus = ref([
    { value: "0", label: "初始提交审核" },
    { value: "1", label: "审核通过" },
    { value: "2", label: "驳回" }
  ]);
  const phytophylumData = ref<any[]>([]); // 门
  const phytoclassData = ref<any[]>([]); // 纲
  const phytoorderData = ref<any[]>([]); // 目
  const phytofamilyData = ref<any[]>([]); // 科
  const phytogenusData = ref<any[]>([]); // 属

  // 获取下拉列表
  const getAllData = async () => {
    const [phytophylum, phytoclass, phytoorder, phytofamily, phytogenus] = await Promise.all([
      baseService.get("/bga/phytophylum/page", params.value),
      baseService.get("/bga/phytoclass/page", params.value),
      baseService.get("/bga/phytoorder/page", params.value),
      baseService.get("/bga/phytofamily/page", params.value),
      baseService.get("/bga/phytogenus/page", params.value)
    ]);
    phytophylumData.value = phytophylum.data.list;
    phytoclassData.value = phytoclass.data.list;
    phytoorderData.value = phytoorder.data.list;
    phytofamilyData.value = phytofamily.data.list;
    phytogenusData.value = phytogenus.data.list;
  };

  onMounted(() => {
    getAllData();
  });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/bga/phytodetail/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 图片预览相关
  const previewVisible = ref(false);
  const previewImage = ref(null);
  const scale = ref(1);
  const position = reactive({ x: 0, y: 0 });
  const isDragging = ref(false);

  const showImagePreview = () => {
    if (dataForm.img) {
      previewVisible.value = true;
      scale.value = 1;
      position.x = 0;
      position.y = 0;
    }
  };

  const closePreview = (event: any) => {
    if (event.target.classList.contains("image-preview")) {
      previewVisible.value = false;
    }
  };

  const handleZoom = (event: WheelEvent) => {
    if (!previewImage.value) return;

    const delta = event.deltaY > 0 ? -0.1 : 0.1;
    scale.value += delta;
    scale.value = Math.max(0.5, Math.min(3, scale.value)); // 限制缩放范围


  };

  const startDrag = (event: MouseEvent) => {
    isDragging.value = true;
    document.addEventListener("mousemove", handleDrag);
    document.addEventListener("mouseup", stopDrag);
  };

  const handleDrag = (event: MouseEvent) => {
    if (!isDragging.value) return;
    position.x += event.movementX;
    position.y += event.movementY;

  };

  const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener("mousemove", handleDrag);
    document.removeEventListener("mouseup", stopDrag);
  };

  defineExpose({
    init
  });
</script>

<style scoped>
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  /* 图片预览样式 */
  .image-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    cursor: zoom-out;
  }

  .preview-container {
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
  }

  .preview-container img {
    max-width: none;
    max-height: none;
    transition: transform 0.3s ease;
  }
</style>
