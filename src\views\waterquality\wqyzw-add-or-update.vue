<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="样品编号（点位+时间）" prop="sampleNumber">
        <el-input v-model="dataForm.sampleNumber" placeholder="样品编号（点位+时间）"></el-input>
      </el-form-item>
      <el-form-item label="监测站点名称" prop="stationName">
        <el-input v-model="dataForm.stationName" placeholder="监测站点名称"></el-input>
      </el-form-item>
      <el-form-item label="年" prop="year">
        <el-input v-model="dataForm.year" placeholder="年"></el-input>
      </el-form-item>
      <el-form-item label="月" prop="month">
        <el-input v-model="dataForm.month" placeholder="月"></el-input>
      </el-form-item>
      <el-form-item label="具体时间" prop="time">
        <el-input v-model="dataForm.time" placeholder="具体时间"></el-input>
      </el-form-item>
      <el-form-item label="水温" prop="waterTemperature">
        <el-input v-model="dataForm.waterTemperature" placeholder="水温"></el-input>
      </el-form-item>
      <el-form-item label="pH无量纲" prop="ph">
        <el-input v-model="dataForm.ph" placeholder="pH无量纲"></el-input>
      </el-form-item>
      <el-form-item label="溶解氧mg/L" prop="dissolvedOxygen">
        <el-input v-model="dataForm.dissolvedOxygen" placeholder="溶解氧mg/L"></el-input>
      </el-form-item>
      <el-form-item label="电导率 (μs/cm)" prop="electricalConductivity">
        <el-input v-model="dataForm.electricalConductivity" placeholder="电导率 (μs/cm)"></el-input>
      </el-form-item>
      <el-form-item label="生化需氧量(mg/L)" prop="biochemicalOxygen">
        <el-input v-model="dataForm.biochemicalOxygen" placeholder="生化需氧量(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="化学需氧量 (mg/L)" prop="chemicalOxygenDemand">
        <el-input v-model="dataForm.chemicalOxygenDemand" placeholder="化学需氧量 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="总磷 (mg/L)" prop="totalPhosphorus">
        <el-input v-model="dataForm.totalPhosphorus" placeholder="总磷 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="总氮 (mg/L)" prop="totalNitrogen">
        <el-input v-model="dataForm.totalNitrogen" placeholder="总氮 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="悬浮物(mg/L)" prop="suspendedSolids">
        <el-input v-model="dataForm.suspendedSolids" placeholder="悬浮物(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="溶解性化学需氧量(mg/L)" prop="dissolvedCod">
        <el-input v-model="dataForm.dissolvedCod" placeholder="溶解性化学需氧量(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="溶解性总磷(mg/L)" prop="solublePhosphorus">
        <el-input v-model="dataForm.solublePhosphorus" placeholder="溶解性总磷(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="溶解性总氮(mg/L)" prop="dissolvedNitrogen">
        <el-input v-model="dataForm.dissolvedNitrogen" placeholder="溶解性总氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="氨氮(mg/L)" prop="ammoniaNitrogen">
        <el-input v-model="dataForm.ammoniaNitrogen" placeholder="氨氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="硝氮(mg/L)" prop="nitrate">
        <el-input v-model="dataForm.nitrate" placeholder="硝氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="亚硝酸氮(mg/L)" prop="nitriteNitrogen">
        <el-input v-model="dataForm.nitriteNitrogen" placeholder="亚硝酸氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="正磷酸盐(mg/L)" prop="orthophosphate">
        <el-input v-model="dataForm.orthophosphate" placeholder="正磷酸盐(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="凯氏氮(mg/L)" prop="kjeldahlNitrogen">
        <el-input v-model="dataForm.kjeldahlNitrogen" placeholder="凯氏氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="叶绿素a" prop="chlorophyllA">
        <el-input v-model="dataForm.chlorophyllA" placeholder="叶绿素a"></el-input>
      </el-form-item>
      <el-form-item label="高锰酸盐指数(mg/L)" prop="permanganateIndex">
        <el-input v-model="dataForm.permanganateIndex" placeholder="高锰酸盐指数(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="溶解性高锰酸盐指数(mg/L)" prop="solublePermanganate">
        <el-input v-model="dataForm.solublePermanganate" placeholder="溶解性高锰酸盐指数(mg/L)"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  const emit = defineEmits(["refreshDataList"]);

  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: '',  sampleNumber: '',  stationName: '',  monitoringUnits: '',  year: '',  month: '', time: '',  waterTemperature: '',  ph: '',  dissolvedOxygen: '',  electricalConductivity: '',  biochemicalOxygen: '',  chemicalOxygenDemand: '',  totalPhosphorus: '',  totalNitrogen: '',  suspendedSolids: '',  dissolvedCod: '',  solublePhosphorus: '',  dissolvedNitrogen: '',  ammoniaNitrogen: '',  nitrate: '',  nitriteNitrogen: '',  orthophosphate: '',  kjeldahlNitrogen: '',  chlorophyllA: '',  permanganateIndex: '',  solublePermanganate: ''});

  const rules = ref({
            sampleNumber: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],

            stationName: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],

            year: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            month: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],

            time: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            waterTemperature: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            ph: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            dissolvedOxygen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            electricalConductivity: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            biochemicalOxygen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            chemicalOxygenDemand: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            totalPhosphorus: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            totalNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            suspendedSolids: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            dissolvedCod: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            solublePhosphorus: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            dissolvedNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            ammoniaNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            nitrate: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            nitriteNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            orthophosphate: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            kjeldahlNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            chlorophyllA: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            permanganateIndex: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            solublePermanganate: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ]
    });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/waterquality/wqyzw/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/waterquality/wqyzw", dataForm).then((res) => {
        ElMessage.success({
          message: '成功',
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
