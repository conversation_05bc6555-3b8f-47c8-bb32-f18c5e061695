<template>
  <div class="mod-bga__phytodatanew">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">{{ $t("上传文件") }}</el-button>
      </el-form-item>
      <el-form-item>
        <img :src="previewState.imageUrl" class="preview-image" @load="previewState.isLoading = false" />
      </el-form-item>
      <el-upload action="http://*************.162:30838/api/v1/minio-upload" class="upload-demo" name="file" :limit="100" ref="upload" :auto-upload="false" :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-form-item>
        <input type="file" webkitdirectory @change="handleFolderSelect" ref="folderInput" accept="image/jpeg,image/jpg" />
        <el-button type="success" @click="handleReadFolderImageUpload">{{ $t("读取文件夹中的图片或视频数据并上传") }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 弹窗, 新增 / 修改 -->
    <div style="display: flex; justify-content: left; align-items: start">
      <el-main>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" style="margin-top: 20px">
          <!--<el-form-item>
            <el-form-item>
              <el-input style="width: 200px" v-model="state.dataForm!.stationName" placeholder="测站名称" clearable></el-input>
            </el-form-item>
            -->
          <!--时间查询-->
          <!--<el-form-item label="开始时间">
            <el-date-picker v-model="state.dataForm!.startTime" type="datetime" placeholder="选择开始时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker v-model="state.dataForm!.endTime" type="datetime" placeholder="选择结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
          </el-form-item>
          <时间查询-->
          <!--<el-form-item>
            <el-button @click="search">{{ $t("query") }}</el-button>
          </el-form-item>

          <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="state.exportHandle()">导出</el-button>
        </el-form-item>
        -->
        </el-form>
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column prop="region" label="区域" header-align="center" align="center"></el-table-column>
          <el-table-column prop="phylumName" label="门名称" header-align="center" align="center"></el-table-column>
          <el-table-column prop="genusName" label="属名称" header-align="center" align="center"></el-table-column>
          <el-table-column prop="speciesName" label="种名称" header-align="center" align="center"></el-table-column>
          <el-table-column prop="description" label="描述" header-align="center" align="center"></el-table-column>
          <el-table-column prop="dataSource" label="数据来源" header-align="center" align="center"></el-table-column>
          <el-table-column prop="imageAddress" label="原始图片地址minio" header-align="center" align="center"></el-table-column>
          <el-table-column prop="labelAddress" label="标注地址" header-align="center" align="center"></el-table-column>
          <el-table-column prop="time" label="日期" header-align="center" align="center"></el-table-column>
          <el-table-column prop="previewAddress" label="预览图片" header-align="center" align="center">
            <template v-slot="scope">
              <!-- 直接绑定图片地址到 src -->
              <img :src="'http://*************:30838/' + scope.row.imageAddress" style="max-width: 100px; max-height: 50px; cursor: pointer" @click="handlePreviewImage(scope.row.imageAddress)" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
            <template v-slot="scope">
              <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
              <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        <el-container style="justify-content: center" v-if="state.dataList && state.dataList.length === 0"> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
      </el-main>
    </div>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="70%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="image-preview-container">
        <img
          v-if="previewImage"
          :src="previewImage"
          style="max-width: 100%; max-height: 80vh; display: block; margin: 0 auto;"
        />
        <el-empty v-else description="图片加载失败"></el-empty>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs} from "vue";
  import AddOrUpdate from "./FileoUpload-add-or-update.vue";
  import { ElLoading, ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";
  import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  import baseService from "@/service/baseService";
  import { useRoute } from "vue-router";
  import axios from "axios";
  import base from "@/router/base"; // 导入 useRoute
  const { $t } = globalLanguage();
  const route = useRoute();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/fileo/fileoupload/page",
    getDataListIsPage: true,
    exportURL: "/fileo/fileoupload/export",
    deleteURL: "/fileo/fileoupload",
    uploadURL: "/fileo/fileoupload"
  });
  const state = reactive({ ...useView(view), ...toRefs(view) });


  const search = () => {
    state.dataForm!.year = "";
    state.dataForm!.month = "";
    state.getDataList();
  };

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };

  const state_excel = reactive({
    selectedFile: null as File | null,
    selectedFiles: [] as Array<{ raw: File; name: string; status: string; path: string }>, // 新增文件夹文件列表
    isUploading: false, // 新增上传状态
    folderName: "", // 新增字段存储文件夹名称
    classificationInfo: {
      phylumName: "", // 存储"门"类名称
      speciesName: "" // 存储"藻"类名称
    }
  });

  //图片上传
  function handleFileChange(file: any, fileList: any) {
    // 当用户选择文件时，更新selectedFile
    if (fileList.length > 0) {
      state_excel.selectedFile = fileList[0].raw; // 使用 fileList[0].raw 获取原始 File 对象
    } else {
      state_excel.selectedFile = null; // 如果没有文件，则设置为 null
    }
  }

  function handleFileUpload() {
    //判断state_excel.selectedFile是否有值
    if (!state_excel.selectedFile) {
      ElMessage.error("请选择文件");
      return;
    }
    const formData = new FormData();
    formData.append("file", state_excel.selectedFile);
    baseService
      .post("files/upload", formData)
      .then((response) => {
        //console.log("测试response.data:", response.data);
        const FilePath = response.data.publicUrl;
        const url = new URL(FilePath);
        const objectName = url.pathname.substring(1);
        console.log("测试FilePath:", objectName);
        baseService.post("/file/fileoupload/saveFileoUploadPath", { filePath: objectName });
        ElMessage.success("上传成功");
        ElMessage.success("文件地址已存储");
      })
      .catch((error) => {
        console.error("错误:", error);
      });
  }
  //读取文件夹数据批量上传测试
  // 修改 handleFolderSelect 函数
  const handleFolderSelect = async (e: Event) => {
    const input = e.target as HTMLInputElement;
    const files = Array.from(input.files || []);

    if (files.length > 0) {
      // 1. 获取文件夹名称（从第一个文件的路径提取）
      const firstFile = files[0];
      const fullPath = getFileRelativePath(firstFile);
      const folderName = fullPath?.split("/")[0] || "未命名文件夹";

      // 2. 分析文件夹结构
      const classification = analyzeFolderStructure(files);

      // 3. 存储分类信息和文件列表
      state_excel.folderName = folderName;
      state_excel.classificationInfo = classification;
      state_excel.selectedFiles = files
        .filter((f) => f.name.match(/\.(jpe?g|png)$/i))
        .map((file) => ({
          raw: file,
          name: file.name,
          path: getFileRelativePath(file), // 存储每个文件的相对路径
          status: "ready"
        }));
    }
  };
  // 新增：安全获取文件相对路径
  function getFileRelativePath(file: File): string {
    // 尝试多种方式获取路径
    return (file as any).webkitRelativePath ||
      (file as any).mozRelativePath ||
      (file as any).relativePath ||
      null;
  }
  // 修改 analyzeFolderStructure 函数
  function analyzeFolderStructure(files: File[]) {
    let phylumName = "";
    let speciesName = "";

    // 分析前3个文件的路径
    const pathSamples = files.slice(0, 3)
      .map(f => getFileRelativePath(f))
      .filter(Boolean) as string[];

    // 从样本路径中提取分类信息
    pathSamples.forEach(path => {
      const segments = path.split("/").slice(0, -1); // 移除文件名

      // 优先从高层级开始匹配
      for (const segment of segments) {
        if (!phylumName && segment.includes("门")) {
          phylumName = segment.trim();
          continue;
        }

        if (!speciesName && segment.includes("藻")) {
          speciesName = segment.trim();
        }
      }
    });

    return { phylumName, speciesName };
  }
  // 修改 handleReadFolderImageUpload 函数
  const handleReadFolderImageUpload = async () => {
    if (state_excel.selectedFiles.length === 0) {
      ElMessage.error("请选择包含图片的文件夹");
      return;
    }

    state_excel.isUploading = true;
    openLoading();

    try {
      const results = await Promise.all(
        state_excel.selectedFiles.map(async (file) => {
          try {
            file.status = "uploading";

            // 1. 获取文件路径（使用预先存储的 path）
            const filePath: string = file.path;
            if (!filePath) {
              ElMessage.warning(`文件 ${file.name} 路径信息缺失`);
              return false;
            }

            // 2. 提取分类信息
            const segments = filePath.split("/").filter(Boolean);
            segments.pop(); // 移除文件名

            const dynamicClassification = {
              phylumName: [...segments].reverse().find(s => s.includes("门")) ||
                state_excel.classificationInfo.phylumName ||
                segments[0] || "未分类门",
              speciesName: [...segments].reverse().find(s => s.includes("藻")) ||
                state_excel.classificationInfo.speciesName ||
                ""
            };

            // 3. 上传文件
            const formData = new FormData();
            formData.append("file", file.raw);

            const res = await axios.post(
              "http://*************:30838/api/v1/minio-upload",
              formData,
              {
                params: {
                  path: dynamicClassification.phylumName.replace(/\s+/g, '_')
                },
                headers: { "Content-Type": "multipart/form-data" }
              }
            );

            // 4. 保存结果
            const ImagePath = res.data.data.filePath;

            await baseService.post("/fileo/fileoupload/saveFileUploadPath", {
              filePath: ImagePath,
              phylumName: dynamicClassification.phylumName,
              speciesName: dynamicClassification.speciesName,
              originalPath: filePath
            });

            file.status = "success";
            return true;
          } catch (e) {
            file.status = "error";
            console.error(`文件 ${file.name} 上传失败:`, e);
            throw e;
          }
        })
      );

      ElMessage.success(`成功上传${results.length}个文件`);
    } catch (error) {
      console.error("批量上传失败:", error);
      ElMessage.error("上传过程中出现错误");
    } finally {
      closeLoading();
      state_excel.isUploading = false;
    }
  };
  const openLoading = () => {
    ElLoading.service({
      lock: true,
      text: "上传中",
      background: "rgba(0, 0, 0, 0.7)"
    });
  };
  const closeLoading = () => {
    ElLoading.service().close();
  };
  const downloadFile = () => {
    const downloadLink = document.createElement("a");
    downloadLink.href = "/PhytoExcelDataImport.xlsx"; // 文件相对于 public 文件夹的路径
    downloadLink.download = "浮游植物Excel数据表模板.xlsx"; // 下载时的文件名

    // 模拟点击下载链接
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  };
  //文件下载测试
  interface DownloadLinkRequest {
    bucketName?: string;
    objectName?: string;
    prefix?: string;
  }
  function handleFileDownLoad(params: DownloadLinkRequest) {
    params.bucketName = "data-bga";
    params.objectName = "550b4d836fDATA.xlsx";
    params.prefix = "data-bga-common/";
    axios
      .get("http://*************:30838/api/v1/minio-upload/download-link", { params: params })
      .then((response) => {
        const DownloadURL = response.data.data;
        console.log("测试DownloadURL", DownloadURL);
        const link = document.createElement("a");
        link.href = DownloadURL;
        link.download = params.objectName ?? "";
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        ElMessage.success("下载成功");
      })
      .catch((error) => {
        console.error("错误:", error);
      });
  }

  //图片预览测试
  // 在组件中声明响应式数据
  const previewState = reactive({
    imageUrl: "", // 图片地址
    isLoading: false, // 加载状态
    errorMsg: "" // 错误信息
  });

  //图片预览放大
  const previewVisible = ref(false);
  const previewImage = ref("");
  const handlePreviewImage = (url: string) => {
    previewImage.value = "http://*************:30838/" + url;
    previewVisible.value = true;
  };
</script>

<style scoped>
  .el-radio-button {
    --el-radio-button-checked-bg-color: #edf9f8;
    --el-radio-button-checked-text-color: #17b3a3;
  }
  ::v-deep .month-card .el-radio-button__inner {
    width: 300px;
    height: 60px;
    font-size: 18px;
    padding-top: 20px;
  }

  .preview-image {
    object-fit: contain; /* 保持比例完整显示 */
    max-width: 20%; /* 防止溢出容器[1,6](@ref) */
    transition: all 0.3s; /* 添加平滑过渡效果 */
  }
</style>
