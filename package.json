{"name": "renren-ui", "version": "5.3.0", "private": true, "scripts": {"dev": "vite", "build": "npm run build:prod", "build:prod": "vue-tsc --noEmit && vite build --mode production", "serve": "npm run build && vite preview", "lint": "eslint \"src/**/*.{vue,ts}\" --fix"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"src/**/*.{ts,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@element-plus/icons-vue": "2.0.6", "@vueuse/core": "9.1.1", "@wangeditor/editor": "5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.27.2", "buffer": "^6.0.3", "classnames": "^2.3.1", "core-js": "^3.14.0", "cors": "^2.8.5", "echarts": "^5.4.3", "element-plus": "2.3.14", "lodash": "^4.17.21", "mitt": "^2.1.0", "nprogress": "^0.2.0", "pinia": "2.0.27", "qs": "^6.10.1", "quill": "^1.3.7", "sm4util": "^1.0.5", "vue": "^3.2.37", "vue-echarts": "^6.0.0", "vue-i18n": "^9.4.1", "vue-router": "4.0.11", "vue3-number-roll-plus": "^0.1.3"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/node": "^22.10.3", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.6", "@types/quill": "^2.0.8", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "4.2.3", "@vue/compiler-sfc": "^3.2.37", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.1", "less-loader": "^10.0.0", "lint-staged": "^11.0.0", "prettier": "^2.6.2", "sass": "^1.50.1", "typescript": "^4.6.3", "vite": "4.4.8", "vite-plugin-html": "^2.1.1", "vite-plugin-svg-icons": "2.0.1", "vite-tsconfig-paths": "3.4.0", "vue-tsc": "1.8.8"}}