<template>
  <div class="tech-container">
    <!-- 返回按钮 -->
    <button class="back-button" @click="goBack">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>返回</span>
    </button>

    <!-- 背景装饰元素 -->
    <div class="bg-glow"></div>
    <div class="grid-pattern"></div>

    <!-- 主内容卡片 - 带增强动态效果和点击返回功能 -->
    <div class="content-card" @click="goBack">
      <!-- 点击提示指示器 -->
      <div class="click-indicator">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M19.4 15C19.2669 15.3016 19.2848 15.6362 19.45 15.916C19.6152 16.1958 19.9181 16.3943 20.24 16.46L20.9 16.65L20.71 17.31C20.6443 17.6319 20.4458 17.9348 20.166 18.0999C19.8862 18.2651 19.5516 18.2829 19.25 18.15L15.5 16.55L14.85 16.85L14.55 16.15L12.95 12.4L12.65 11.75L11.95 12.05L8.2 10.45L7.85 9.8L8.15 9.1L6.55 5.35C6.42691 5.04839 6.44474 4.71382 6.61 4.43401C6.77526 4.1542 7.07814 3.95568 7.4 3.89L8.06 3.7L8.25 4.36C8.31568 4.68186 8.5142 4.98474 8.79401 5.15C9.07382 5.31526 9.40839 5.33309 9.71 5.21L13.46 3.6L14.11 3.9L14.41 4.55L12.8 8.3C12.6769 8.60161 12.6591 8.93618 12.4939 9.21599C12.3287 9.4958 12.1302 9.79868 12.0645 10.1205L11.875 10.78L12.535 10.5905C12.8573 10.5253 13.1607 10.7245 13.4405 10.8902C13.7203 11.0554 14.0548 11.0732 14.3564 10.94L18.1 9.35L18.75 9.65L19.05 10.3L17.45 14.05C17.3269 14.3516 17.3447 14.6862 17.51 14.966C17.6753 15.2458 17.9781 15.4443 18.3 15.51L18.96 15.7L19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>点击返回</span>
      </div>

      <!-- 动态加载图案 -->
      <div class="loading-icon">
        <div class="loader-circle">
          <div class="loader-ring"></div>
          <div class="loader-center"></div>
        </div>
      </div>

      <!-- 动态提示词 -->
      <h1 class="main-title dynamic-text">功能开发中</h1>
      <p class="message dynamic-text-sub">该功能暂未开发完成，敬请期待</p>

      <div class="progress-indicator">
        <span class="dot" v-for="i in 3" :key="i"></span>
      </div>
    </div>

    <!-- 底部装饰条 -->
    <div class="bottom-bar"></div>
  </div>
</template>

<script>
  export default {
    name: 'TechNotDeveloped',
    data() {
      return {
        brightnessInterval: null,
        hue: 0, // 初始色相 (从0开始，彩虹色循环)
        textAnimationInterval: null
      }
    },
    methods: {
      // 返回上一页方法
      goBack() {
        window.history.back();
      },

      // HSV转RGB工具函数
      hsvToRgb(h, s, v) {
        let r, g, b;
        const i = Math.floor(h / 60);
        const f = h / 60 - i;
        const p = v * (1 - s);
        const q = v * (1 - f * s);
        const t = v * (1 - (1 - f) * s);

        switch (i % 6) {
          case 0: r = v; g = t; b = p; break;
          case 1: r = q; g = v; b = p; break;
          case 2: r = p; g = v; b = t; break;
          case 3: r = p; g = q; b = v; break;
          case 4: r = t; g = p; b = v; break;
          case 5: r = v; g = p; b = q; break;
        }

        return {
          r: Math.round(r * 255),
          g: Math.round(g * 255),
          b: Math.round(b * 255)
        };
      }
    },
    mounted() {
      // 控制加载点动画
      const dots = document.querySelectorAll('.dot');
      setInterval(() => {
        dots.forEach(dot => {
          dot.style.opacity = Math.random() * 0.7 + 0.3;
        });
      }, 800);

      // 控制卡片动态亮度和颜色效果
      const card = document.querySelector('.content-card');
      const mainTitle = document.querySelector('.main-title');
      const subTitle = document.querySelector('.message');
      let brightness = 1;
      let brightnessDir = -0.02;
      let hueDir = 2; // 彩虹色变化速度

      this.brightnessInterval = setInterval(() => {
        // 亮度变化
        brightness += brightnessDir;
        if (brightness <= 0.6) {
          brightness = 0.6;
          brightnessDir = 0.02;
        } else if (brightness >= 1.4) {
          brightness = 1.4;
          brightnessDir = -0.02;
        }

        // 色相变化（彩虹色循环：0-360度）
        this.hue += hueDir;
        if (this.hue >= 360) {
          this.hue = 0; // 循环回到红色
        }

        // 应用亮度和颜色变化到卡片
        card.style.filter = `brightness(${brightness})`;
        const saturation = 70;
        const lightness = 50 + (brightness - 1) * 10;
        const shadowIntensity = 0.1 + (brightness - 0.6) * 0.3;

        const rgbColor = this.hsvToRgb(this.hue, saturation / 100, lightness / 100);
        card.style.boxShadow = `0 0 40px rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, ${shadowIntensity})`;
        card.style.borderColor = `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, ${0.4 + (brightness - 0.6) * 0.5})`;

        // 更新加载图标颜色
        const loaderRing = document.querySelector('.loader-ring');
        loaderRing.style.borderColor = `rgba(255, 255, 255, 0.1) rgba(255, 255, 255, 0.1) rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.8) rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.8)`;

        // 应用彩虹色到提示文字（主标题饱和度更高，更鲜艳）
        const titleRgb = this.hsvToRgb(this.hue, 90 / 100, 70 / 100);
        mainTitle.style.color = `rgb(${titleRgb.r}, ${titleRgb.g}, ${titleRgb.b})`;
        mainTitle.style.textShadow = `0 0 10px rgba(${titleRgb.r}, ${titleRgb.g}, ${titleRgb.b}, 0.5)`;

        // 副标题使用相同色相但饱和度较低
        const subRgb = this.hsvToRgb(this.hue, 60 / 100, 60 / 100);
        subTitle.style.color = `rgb(${subRgb.r}, ${subRgb.g}, ${subRgb.b})`;

      }, 60);

      // 提示文字动态效果（位置浮动）
      let titleOffset = 0;
      let titleDir = 1;

      this.textAnimationInterval = setInterval(() => {
        // 轻微上下浮动效果
        titleOffset += titleDir * 0.1;
        if (titleOffset > 1) {
          titleDir = -1;
        } else if (titleOffset < -1) {
          titleDir = 1;
        }

        mainTitle.style.transform = `translateY(${titleOffset}px)`;
        subTitle.style.transform = `translateY(${-titleOffset}px)`;

        // 呼吸效果
        const opacity = 0.9 + Math.sin(Date.now() / 1000) * 0.1;
        mainTitle.style.opacity = opacity;
        subTitle.style.opacity = 0.8 + Math.sin(Date.now() / 1200 + 1) * 0.1;
      }, 50);

      // 卡片鼠标交互
      card.addEventListener('mouseenter', () => {
        card.style.cursor = 'pointer';
        clearInterval(this.brightnessInterval);
        clearInterval(this.textAnimationInterval);

        card.style.filter = 'brightness(1.6)';
        const rgbColor = this.hsvToRgb(this.hue, 70, 60);
        card.style.boxShadow = `0 0 50px rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.5)`;
        card.style.borderColor = `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.8)`;

        // 悬停时文字效果（保持彩虹色）
        const titleRgb = this.hsvToRgb(this.hue, 90 / 100, 70 / 100);
        mainTitle.style.color = `rgb(${titleRgb.r}, ${titleRgb.g}, ${titleRgb.b})`;
        mainTitle.style.textShadow = `0 0 15px rgba(${titleRgb.r}, ${titleRgb.g}, ${titleRgb.b}, 0.7)`;

        mainTitle.style.transform = 'translateY(-3px) scale(1.02)';
        mainTitle.style.opacity = 1;

        const subRgb = this.hsvToRgb(this.hue, 60 / 100, 60 / 100);
        subTitle.style.color = `rgb(${subRgb.r}, ${subRgb.g}, ${subRgb.b})`;
        subTitle.style.transform = 'translateY(3px)';
        subTitle.style.opacity = 0.9;
      });

      card.addEventListener('mouseleave', () => {
        brightness = 1;
        brightnessDir = -0.02;
        this.mounted(); // 恢复动画
      });
    },
    beforeUnmount() {
      clearInterval(this.brightnessInterval);
      clearInterval(this.textAnimationInterval);
    }
  };
</script>

<style scoped>
  .tech-container {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  /* 返回按钮样式 */
  .back-button {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 4;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: #e2e8f0;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
  }

  .back-button:hover {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(99, 102, 241, 0.5);
    transform: translateX(-3px);
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.2);
  }

  /* 背景装饰元素 */
  .bg-glow {
    position: absolute;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, rgba(15, 23, 42, 0) 70%);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  .grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 2;
  }

  /* 主内容卡片 */
  .content-card {
    position: relative;
    z-index: 3;
    background: rgba(30, 41, 59, 0.7);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 16px;
    padding: 40px 60px;
    text-align: center;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  /* 点击提示指示器 */
  .click-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    color: rgba(148, 163, 184, 0.7);
    font-size: 0.8rem;
    animation: fadeInOut 2s infinite;
  }

  @keyframes fadeInOut {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }

  /* 动态加载图案样式 */
  .loading-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 30px;
    position: relative;
  }

  .loader-circle {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .loader-ring {
    box-sizing: border-box;
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #6366f1;
    border-right-color: #6366f1;
    animation: rotate 2s linear infinite;
    filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.6));
  }

  .loader-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30%;
    height: 30%;
    background-color: rgba(99, 102, 241, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.8);
    animation: pulse 2s ease-in-out infinite;
  }

  /* 加载动画 */
  @keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
  }

  /* 动态提示词样式 */
  .main-title {
    /* 移除固定颜色，由JS动态控制 */
    font-size: 2.2rem;
    margin: 0 0 15px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }

  .message {
    /* 移除固定颜色，由JS动态控制 */
    font-size: 1.1rem;
    margin: 0 0 30px;
    line-height: 1.6;
    transition: all 0.3s ease;
  }

  .content-card:hover .main-title {
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  }

  /* 加载指示器 */
  .progress-indicator {
    display: flex;
    justify-content: center;
    gap: 10px;
  }

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #6366f1;
    opacity: 0.4;
    transition: all 0.3s ease;
  }

  /* 底部装饰 */
  .bottom-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6, #3b82f6);
    background-size: 400% 100%;
    animation: barMove 8s linear infinite;
    z-index: 4;
  }

  @keyframes barMove {
    0% { background-position: 0% 0; }
    100% { background-position: 400% 0; }
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .content-card {
      padding: 30px 40px;
    }

    .main-title {
      font-size: 1.8rem;
    }

    .back-button {
      top: 20px;
      left: 20px;
      padding: 8px 12px;
      font-size: 0.9rem;
    }

    .click-indicator {
      font-size: 0.7rem;
    }

    .loading-icon {
      width: 60px;
      height: 60px;
    }
  }
</style>
