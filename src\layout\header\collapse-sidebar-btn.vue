<script lang="ts">
import SvgIcon from "@/components/base/svg-icon";
import { EMitt, EThemeSetting } from "@/constants/enum";
import emits from "@/utils/emits";
import { getThemeConfigCacheByKey, setThemeConfigToCache } from "@/utils/theme";
import { defineComponent, reactive } from "vue";
import { useRoute } from "vue-router";

/**
 * PC和移动端下的侧边栏展开收起按钮
 */
export default defineComponent({
  name: "CollapseSidebarBtn",
  components: { SvgIcon },
  setup() {
    const route = useRoute();
    const state = reactive({
      collapseSidebar: getThemeConfigCacheByKey(EThemeSetting.SidebarCollapse),
      colorIcon: getThemeConfigCacheByKey(EThemeSetting.ColorIcon)
    });
    const onClickSidebarSwitcher = () => {
      const key = EThemeSetting.SidebarCollapse;
      state.collapseSidebar = !state.collapseSidebar;
      emits.emit(EMitt.OnSwitchLeftSidebar);
      emits.emit(EMitt.OnSetTheme, [key, key + "-" + state.collapseSidebar]);
      setThemeConfigToCache(key, state.collapseSidebar);
      if (route.path === "/home") {
        emits.emit(EMitt.OnReloadTabPage);
      }
    };
    const onClickSidebarSwitcherByMobile = () => {
      emits.emit(EMitt.OnMobileOpenSidebar);
      if (route.path === "/home") {
        emits.emit(EMitt.OnReloadTabPage);
      }
    };
    return { state, onClickSidebarSwitcher, onClickSidebarSwitcherByMobile };
  }
});
</script>
<template>
  <div class="hidden-xs-only" @click="onClickSidebarSwitcher">
    <svg-icon :name="state.collapseSidebar ? 'indent' : 'outdent'"></svg-icon>
  </div>
  <div class="hidden-sm-and-up show-xs-only" @click="onClickSidebarSwitcherByMobile">
    <svg-icon name="icon-indent"></svg-icon>
  </div>
</template>
