<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="uploadBgaDataHandle()">{{ $t("phytoplankto.uploadDataTable") }}</el-button>
      </el-form-item>
      <el-upload
        action="/bga/phytosampledata/excel-input"
        class="upload-demo"
        name="file"
        :limit="1"
        ref="upload"
        :auto-upload="false"
        accept=".xlsx,.xls,.xlsm"
        :data="{ metaid: route.meta.routerId }"
        drag
        @change="handleFileChange"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
      <el-button type="danger" @click="handleFileUpload">{{ $t("导入Excel数据至数据库") }}</el-button>
    </el-form>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      @selection-change="state.dataListSelectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        label="id"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="stationId"
        :label="$t('phytoplankto.samplingId')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="samplingLocation"
        :label="$t('phytoplankto.samplingLocation')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="sampleVolumeLiters"
        :label="$t('phytoplankto.sampleVolumeLiters')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="concentratedVolume"
        :label="$t('phytoplankto.concentratedVolume')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="dilutionFactor"
        :label="$t('phytoplankto.dilutionFactor')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="fieldOfViewCount"
        :label="$t('phytoplankto.fieldOfViewCount')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="fieldArea"
        :label="$t('phytoplankto.fieldArea')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="countingArea"
        :label="$t('phytoplankto.countingArea')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="countingFrameVolume"
        :label="$t('phytoplankto.countingFrameVolume')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="150"
        prop="countingFrameArea"
        :label="$t('phytoplankto.countingFrameArea')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="160"
        prop="samplingAt"
        :label="$t('phytoplankto.samplingAt')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="160"
        prop="createdAt"
        :label="$t('bga.createDate')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="160"
        prop="updateAt"
        :label="$t('bga.updateDate')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="160"
        prop="deleteAt"
        :label="$t('bga.deleteDate')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        width="160"
        prop="dataSource"
        :label="$t('数据来源')"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        :label="$t('handle')"
        fixed="right"
        header-align="center"
        align="center"
        width="150"
      >
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            @click="addOrUpdateHandle(scope.row.id)"
          >{{ $t("update") }}</el-button
          >
          <el-button
            type="primary"
            link
            @click="state.deleteHandle(scope.row.id)"
          >{{ $t("delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="state.limit"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="state.pageSizeChangeHandle"
      @current-change="state.pageCurrentChangeHandle"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">
      {{ $t("confirm") }}
    </add-or-update>
    <update-bga-data
      :uploadPath="state.uploadURL"
      ref="updateBgaDataRef"
      @refreshDataList="state.getDataList"
    ></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./phytosampledata-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { useRoute } from "vue-router";

  const route = useRoute();
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/phytosampledata/page",
    getDataListIsPage: true,
    exportURL: "/bga/phytosampledata/export",
    deleteURL: "/bga/phytosampledata",
    uploadURL: "/bga/phytosampledata/upload",
    ExcelURL: "/bga/phytosampledata/excel"
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };

  const state_excel = reactive<any>({
    selectedFile: null
  });

  function handleFileChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      state_excel.selectedFile = fileList[0].raw;
    } else {
      state_excel.selectedFile = null;
    }
  }

  function handleFileUpload() {
    const formData = new FormData();
    formData.append("file", state_excel.selectedFile);

    baseService
      .post("/bga/phytosampledata/excel-input", formData)
      .then((response) => {
        ElMessage.success("导入成功");
        state.getDataList();
      })
      .catch((error) => {
        ElMessage.error("导入失败");
        console.error("错误:", error);
      });
  }
</script>
