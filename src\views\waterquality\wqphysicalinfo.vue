<template>
  <div class="mod-bga__wqphysicalinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('waterquality:wqphysicalinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('waterquality:wqphysicalinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('waterquality.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="verticalLineNum" :label="$t('waterquality.verticalLineNum')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="layerNum" :label="$t('waterquality.layerNum')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="waterTemperature" :label="$t('waterquality.waterTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ph" :label="$t('waterquality.ph')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="electricalConductivity" :label="$t('waterquality.electricalConductivity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="turbidity" :label="$t('waterquality.turbidity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dissolvedOxygen" :label="$t('waterquality.dissolvedOxygen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="transparency" :label="$t('waterquality.transparency')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="orp" :label="$t('waterquality.orp')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="underwaterIllumination" :label="$t('waterquality.underwaterIllumination')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="suspendedSolidsConcentration" :label="$t('waterquality.suspendedSolidsConcentration')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('waterquality.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('waterquality.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('waterquality:wqphysicalinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('waterquality:wqphysicalinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./wqphysicalinfo-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/waterquality/wqphysicalinfo/page",
    getDataListIsPage: true,
    exportURL: "/waterquality/wqphysicalinfo/export",
    deleteURL: "/waterquality/wqphysicalinfo"
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
</script>
