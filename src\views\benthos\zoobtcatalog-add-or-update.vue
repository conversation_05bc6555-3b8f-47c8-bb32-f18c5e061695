<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('benthos.zoobtPhylumName')" prop="zoobtPhylumName">
        <el-input v-model="dataForm.zoobtPhylumName" :placeholder="$t('benthos.zoobtPhylumName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtPhylumLatinName')" prop="zoobtPhylumLatinName">
        <el-input v-model="dataForm.zoobtPhylumLatinName" :placeholder="$t('benthos.zoobtPhylumLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtClassName')" prop="zoobtClassName">
        <el-input v-model="dataForm.zoobtClassName" :placeholder="$t('benthos.zoobtClassName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtClassLatinName')" prop="zoobtClassLatinName">
        <el-input v-model="dataForm.zoobtClassLatinName" :placeholder="$t('benthos.zoobtClassLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtOrderName')" prop="zoobtOrderName">
        <el-input v-model="dataForm.zoobtOrderName" :placeholder="$t('benthos.zoobtOrderName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtOrderLatinName')" prop="zoobtOrderLatinName">
        <el-input v-model="dataForm.zoobtOrderLatinName" :placeholder="$t('benthos.zoobtOrderLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtFamilyName')" prop="zoobtFamilyName">
        <el-input v-model="dataForm.zoobtFamilyName" :placeholder="$t('benthos.zoobtFamilyName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtFamilyLatinName')" prop="zoobtFamilyLatinName">
        <el-input v-model="dataForm.zoobtFamilyLatinName" :placeholder="$t('benthos.zoobtFamilyLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtGenusName')" prop="zoobtGenusName">
        <el-input v-model="dataForm.zoobtGenusName" :placeholder="$t('benthos.zoobtGenusName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtGenusLatinName')" prop="zoobtGenusLatinName">
        <el-input v-model="dataForm.zoobtGenusLatinName" :placeholder="$t('benthos.zoobtGenusLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtSpeciesName')" prop="zoobtSpeciesName">
        <el-input v-model="dataForm.zoobtSpeciesName" :placeholder="$t('benthos.zoobtSpeciesName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtSpeciesLatinName')" prop="zoobtSpeciesLatinName">
        <el-input v-model="dataForm.zoobtSpeciesLatinName" :placeholder="$t('benthos.zoobtSpeciesLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('benthos.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.updateAt')" prop="updateAt">
        <el-input v-model="dataForm.updateAt" :placeholder="$t('benthos.updateAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.deleteAt')" prop="deleteAt">
        <el-input v-model="dataForm.deleteAt" :placeholder="$t('benthos.deleteAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('benthos.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  zoobtPhylumName: "",
  zoobtPhylumLatinName: "",
  zoobtClassName: "",
  zoobtClassLatinName: "",
  zoobtOrderName: "",
  zoobtOrderLatinName: "",
  zoobtFamilyName: "",
  zoobtFamilyLatinName: "",
  zoobtGenusName: "",
  zoobtGenusLatinName: "",
  zoobtSpeciesName: "",
  zoobtSpeciesLatinName: "",
  createdAt: "",
  updateAt: "",
  deleteAt: "",
  remark: ""
});

const rules = ref({
  zoobtPhylumName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtPhylumLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtClassName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtClassLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtOrderName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtOrderLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtFamilyName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtFamilyLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtGenusName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtGenusLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtSpeciesName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtSpeciesLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  updateAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  deleteAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/benthos/zoobtcatalog/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/benthos/zoobtcatalog", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
