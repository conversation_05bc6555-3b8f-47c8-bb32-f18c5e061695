<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('phytoplankto.test1')" prop="samplingId">
        <el-input v-model="dataForm.samplingId" :placeholder="$t('phytoplankto.test1')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test2')" prop="stationId">
        <el-input v-model="dataForm.stationId" :placeholder="$t('phytoplankto.test2')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test3')" prop="stationName">
        <el-input v-model="dataForm.stationName" :placeholder="$t('phytoplankto.test3')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test4')" prop="timeYear">
        <el-input v-model="dataForm.timeYear" :placeholder="$t('phytoplankto.test4')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test5')" prop="timeMonth">
        <el-input v-model="dataForm.timeMonth" :placeholder="$t('phytoplankto.test5')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test6')" prop="timeDay">
        <el-input v-model="dataForm.timeDay" :placeholder="$t('phytoplankto.test6')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test7')" prop="timeDetail">
        <el-input v-model="dataForm.timeDetail" :placeholder="$t('phytoplankto.test7')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test8')" prop="samplingVolume">
        <el-input v-model="dataForm.samplingVolume" :placeholder="$t('phytoplankto.test8')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test9')" prop="concentratedVolume">
        <el-input v-model="dataForm.concentratedVolume" :placeholder="$t('phytoplankto.test9')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test10')" prop="dilutionRatio">
        <el-input v-model="dataForm.dilutionRatio" :placeholder="$t('phytoplankto.test10')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test11')" prop="fieldNumber">
        <el-input v-model="dataForm.fieldNumber" :placeholder="$t('phytoplankto.test11')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test12')" prop="fieldArea">
        <el-input v-model="dataForm.fieldArea" :placeholder="$t('phytoplankto.test12')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test13')" prop="phytoPhylum">
        <el-input v-model="dataForm.phytoPhylum" :placeholder="$t('phytoplankto.test13')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test14')" prop="phytoClass">
        <el-input v-model="dataForm.phytoClass" :placeholder="$t('phytoplankto.test14')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test15')" prop="phytoOrder">
        <el-input v-model="dataForm.phytoOrder" :placeholder="$t('phytoplankto.test15')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test16')" prop="phytoFamily">
        <el-input v-model="dataForm.phytoFamily" :placeholder="$t('phytoplankto.test16')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test17')" prop="phytoGenus">
        <el-input v-model="dataForm.phytoGenus" :placeholder="$t('phytoplankto.test17')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test18')" prop="phytoCategory">
        <el-input v-model="dataForm.phytoCategory" :placeholder="$t('phytoplankto.test18')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test19')" prop="phytoLatin">
        <el-input v-model="dataForm.phytoLatin" :placeholder="$t('phytoplankto.test19')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test20')" prop="wetWeight">
        <el-input v-model="dataForm.wetWeight" :placeholder="$t('phytoplankto.test20')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test21')" prop="phytoNumber">
        <el-input v-model="dataForm.phytoNumber" :placeholder="$t('phytoplankto.test21')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test22')" prop="cellNumber">
        <el-input v-model="dataForm.cellNumber" :placeholder="$t('phytoplankto.test22')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.test23')" prop="biomass">
        <el-input v-model="dataForm.biomass" :placeholder="$t('phytoplankto.test23')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  samplingId: "",
  stationId: "",
  stationName: "",
  timeYear: "",
  timeMonth: "",
  timeDay: "",
  timeDetail: "",
  samplingVolume: "",
  concentratedVolume: "",
  dilutionRatio: "",
  fieldNumber: "",
  fieldArea: "",
  phytoPhylum: "",
  phytoClass: "",
  phytoOrder: "",
  phytoFamily: "",
  phytoGenus: "",
  phytoCategory: "",
  phytoLatin: "",
  wetWeight: "",
  phytoNumber: "",
  cellNumber: "",
  biomass: ""
});

const rules = ref({
  samplingId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeYear: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeMonth: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDay: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDetail: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingVolume: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  concentratedVolume: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  dilutionRatio: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  fieldNumber: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  fieldArea: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoPhylum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoClass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoOrder: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoFamily: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoGenus: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoCategory: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoLatin: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  wetWeight: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  phytoNumber: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  cellNumber: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  bioMass: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/bga/phytodatanew/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/bga/phytodatanew", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
