<template>
  <div class="mod-bga__zoobtaveragebiomass">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('benthos:zoobtaveragebiomass:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('benthos:zoobtaveragebiomass:delete')" type="danger" @click="state.deleteHandle()">{{$t('delete') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtId" :label="$t('benthos.zoobtId')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtName" :label="$t('benthos.zoobtName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('benthos.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationName" :label="$t('benthos.stationName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="averageBiomass" :label="$t('benthos.averageBiomass')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('benthos.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="updateAt" :label="$t('benthos.updateAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="deleteAt" :label="$t('benthos.deleteAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('benthos.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('benthos:zoobtaveragebiomass:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('benthos:zoobtaveragebiomass:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zoobtaveragebiomass-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/benthos/zoobtaveragebiomass/page",
  getDataListIsPage: true,
  exportURL: "/benthos/zoobtaveragebiomass/export",
  deleteURL: "/benthos/zoobtaveragebiomass"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
