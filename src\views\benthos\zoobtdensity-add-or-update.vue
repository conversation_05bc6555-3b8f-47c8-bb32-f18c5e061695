<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('benthos.zoobtId')" prop="zoobtId">
        <el-input v-model="dataForm.zoobtId" :placeholder="$t('benthos.zoobtId')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtName')" prop="zoobtName">
        <el-input v-model="dataForm.zoobtName" :placeholder="$t('benthos.zoobtName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('benthos.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.stationName')" prop="stationName">
        <el-input v-model="dataForm.stationName" :placeholder="$t('benthos.stationName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.density')" prop="density">
        <el-input v-model="dataForm.density" :placeholder="$t('benthos.density')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.latitudeAndLongitude')" prop="latitudeAndLongitude">
        <el-input v-model="dataForm.latitudeAndLongitude" :placeholder="$t('benthos.latitudeAndLongitude')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.samplingTime')" prop="samplingTime">
        <el-input v-model="dataForm.samplingTime" :placeholder="$t('benthos.samplingTime')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.samplingLocation')" prop="samplingLocation">
        <el-input v-model="dataForm.samplingLocation" :placeholder="$t('benthos.samplingLocation')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.samplingTool')" prop="samplingTool">
        <el-input v-model="dataForm.samplingTool" :placeholder="$t('benthos.samplingTool')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtSize')" prop="zoobtSize">
        <el-input v-model="dataForm.zoobtSize" :placeholder="$t('benthos.zoobtSize')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('benthos.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.updateAt')" prop="updateAt">
        <el-input v-model="dataForm.updateAt" :placeholder="$t('benthos.updateAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.deleteAt')" prop="deleteAt">
        <el-input v-model="dataForm.deleteAt" :placeholder="$t('benthos.deleteAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('benthos.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  zoobtId: "",
  zoobtName: "",
  stationCode: "",
  stationName: "",
  density: "",
  latitudeAndLongitude: "",
  samplingTime: "",
  samplingLocation: "",
  samplingTool: "",
  zoobtSize: "",
  createdAt: "",
  updateAt: "",
  deleteAt: "",
  remark: ""
});

const rules = ref({
  zoobtId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  stationName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  density: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  latitudeAndLongitude: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingTime: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingLocation: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingTool: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtSize: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  updateAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  deleteAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/benthos/zoobtdensity/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/benthos/zoobtdensity", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
