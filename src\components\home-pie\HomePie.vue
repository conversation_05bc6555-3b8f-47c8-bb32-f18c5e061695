<template>
  <div ref="chart" style="width: 100%; height: 700px"></div>
</template>

<script setup>
import * as echarts from "echarts";
// 创建图表
import { ref, onMounted, reactive } from "vue";
import baseService from "@/service/baseService";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
import { useAppStore } from "@/store";
const store = useAppStore();
const addressList = store.state.address.list.map((item) => {
  return { address: $t(`address.${item.address}`), addressId: item.id };
});

// 数据和颜色信息
const chartdata = [];
const dataForm = reactive({
  order: "desc",
  orderField: "create_date",
  page: 1,
  limit: 10,
  fileName: "",
  address: "",
  addressId: "",
  createDate: ""
});
// 随机生成气泡的坐标
const randomPositions = () => {
  const positions = [];
  for (let i = 0; i < addressList.length; i++) {
    positions.push([
      Math.random() * 100, // x 轴位置
      Math.random() * 100 // y 轴位置
    ]);
  }
  return positions;
};
const getData = async () => {
  const positions = randomPositions();
  await Promise.all(
    addressList.map(async (item) => {
      const { data } = await baseService.get("/oss/files/page", { ...dataForm, ...item });
      chartdata.push({
        name: item.address,
        size: 120,
        sum: data.total,
        value: positions[addressList.indexOf(item.address)]
        // 如果有其他需要处理的数据，请在这里进行
      });
      return data;
    })
  );
  const newData = chartdata.map((item) => {
    return {
      name: item.name + "\n\n" + item.sum,
      value: item.value,
      symbolSize: item.size,
      draggable: true,
      label: {
        normal: {
          textStyle: {
            fontSize: 14,
            color: "#fff"
          }
        }
      },
      itemStyle: {
        normal: {
          color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
            {
              offset: 0.2,
              color: "rgba(27, 54, 72, 0.2)"
            },
            {
              offset: 0.8,
              color: "#56C7F6"
            }
          ]),
          opacity: 1,
          borderWidth: 3,
          borderColor: "#56C7F6",
          shadowBlur: 7,
          symbolOffset: 0.6,
          shadowColor: "#56C7F6"
        }
      }
    };
  });
  const myChart = echarts.init(chart.value);
  const option = {
    backgroundColor: "#17B3A3",
    animationDurationUpdate: function (idx) {
      return idx * 500;
    },
    tooltip: {
      trigger: "item",
      formatter: function (params, ticket, callback) {
        return params.data.tips || params.name;
      }
    },
    animationEasingUpdate: "bounceIn",
    itemStyle: {},
    cursor: "pointer",
    series: [
      {
        type: "graph",
        layout: "force",
        cursor: "pointer",
        force: {
          repulsion: 500,
          edgeLength: 150
        },
        roam: true,
        label: {
          normal: {
            show: true
          }
        },
        data: newData
      }
    ]
  };
  myChart.setOption(option);
};

const chart = ref(null);
onMounted(() => {
  getData();
});
</script>
