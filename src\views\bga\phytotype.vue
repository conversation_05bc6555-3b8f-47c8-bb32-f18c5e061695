<template>
  <div class="mod-bga__phytotype">
    <el-row :gutter="12" v-loading="state1.dataListLoading">
      <el-col :span="4">
        <div class="phyto">
          <el-card shadow="always" class="first" :body-style="Card_Style_Header"> 门 </el-card>
          <el-scrollbar height="700">
            <el-card
              v-for="item in phytophylum"
              @click="changeSelectPhytolum(item.nameCn)"
              :key="item.id"
              shadow="hover"
              :body-style="Card_Style_Body"
              :class="{ active: phytophylumSelect === item.nameCn }"
            >
              {{ item.nameCn }}
            </el-card>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="phyto">
          <el-card shadow="always" class="first" :body-style="Card_Style_Header"> 纲 </el-card>
          <el-scrollbar height="700">
            <el-card
              v-for="item in phytoclass"
              @click="changeSelectPhytoClass(item.nameCn)"
              :key="item.id"
              shadow="hover"
              :body-style="Card_Style_Body"
              :class="{ active: phytoclassSelect === item.nameCn }"
            >
              {{ item.nameCn }}
            </el-card>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="phyto">
          <el-card shadow="always" class="first" :body-style="Card_Style_Header"> 目 </el-card>
          <el-scrollbar height="700">
            <el-card
              v-for="item in phytoorder"
              @click="changeSelectPhytoOrder(item.nameCn)"
              :key="item.id"
              shadow="hover"
              :body-style="Card_Style_Body"
              :class="{ active: phytoorderSelect === item.nameCn }"
            >
              {{ item.nameCn }}
            </el-card>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="phyto">
          <el-card shadow="always" class="first" :body-style="Card_Style_Header"> 科 </el-card>
          <el-scrollbar height="700">
            <el-card
              v-for="item in phytofamily"
              @click="changeSelectPhytoFamily(item.nameCn)"
              :key="item.id"
              shadow="hover"
              :body-style="Card_Style_Body"
              :class="{ active: phytofamilySelect === item.nameCn }"
            >
              {{ item.nameCn }}
            </el-card>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="phyto">
          <el-card shadow="always" class="first" :body-style="Card_Style_Header"> 属 </el-card>
          <el-scrollbar height="700">
            <el-card
              v-for="item in phytogenus"
              @click="changeSelectPhytogenus(item.nameCn)"
              :key="item.id"
              shadow="hover"
              :body-style="Card_Style_Body"
              :class="{ active: phytogenusSelect === item.nameCn }"
            >
              {{ item.nameCn }}
            </el-card>
          </el-scrollbar>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs, onMounted } from "vue";
  import AddOrUpdate from "./phytodetail-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import baseService from "@/service/baseService";
  import { Phyto } from "@/types/interface";
  const { $t } = globalLanguage();

  // 蓝藻门
  const view1 = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/phytodetail/page",
    getDataListIsPage: true,
    exportURL: "/bga/phytodetail/export",
    deleteURL: "/bga/phytodetail",
  });
  const state1 = reactive({ ...useView(view1), ...toRefs(view1) });

  // 选择门
  const changeSelectPhytolum = (nameCn: string) => {
    phytophylumSelect.value = nameCn;
    fetchData(nameCn).then((response) => {
      console.log(response);
    });
  };

  // 选择纲
  const changeSelectPhytoClass = (nameCn: string) => {
    phytoclassSelect.value = nameCn;
    // 这里可以添加获取纲下数据的逻辑
  };

  // 选择目
  const changeSelectPhytoOrder = (nameCn: string) => {
    phytoorderSelect.value = nameCn;
    // 这里可以添加获取目下数据的逻辑
  };

  // 选择科
  const changeSelectPhytoFamily = (nameCn: string) => {
    phytofamilySelect.value = nameCn;
    // 这里可以添加获取科下数据的逻辑
  };

  // 选择属
  const changeSelectPhytogenus = (nameCn: string) => {
    phytogenusSelect.value = nameCn;
    // 这里可以添加获取属下数据的逻辑
  };

  const phytophylum = ref<Phyto[]>([]);
  const phytoclass = ref<Phyto[]>([]);
  const phytoorder = ref<Phyto[]>([]);
  const phytofamily = ref<Phyto[]>([]);
  const phytogenus = ref<Phyto[]>([]);
  const phytostrain = ref<Phyto[]>([]);
  const phytophylumSelect = ref("");
  const phytoclassSelect = ref("");
  const phytoorderSelect = ref("");
  const phytofamilySelect = ref("");
  const phytogenusSelect = ref("");
  const phytostrainSelect = ref("");

  onMounted(async () => {
    // 获取门数据
    const { data } = await baseService.get("/bga/phytophylum/page", {
      page: 1,
      limit: 99999,
    });
    phytophylum.value = data.list;

    // 获取纲数据
    const { data: classData } = await baseService.get("/bga/phytoclass/page", {
      page: 1,
      limit: 99999,
    });
    phytoclass.value = classData.list;

    // 获取目数据
    const { data: orderData } = await baseService.get("/bga/phytoorder/page", {
      page: 1,
      limit: 99999,
    });
    phytoorder.value = orderData.list;

    // 获取科数据
    const { data: familyData } = await baseService.get("/bga/phytofamily/page", {
      page: 1,
      limit: 99999,
    });
    phytofamily.value = familyData.list;

    // 获取属数据
    const { data: genusData } = await baseService.get("/bga/phytogenus/page", {
      page: 1,
      limit: 99999,
    });
    phytogenus.value = genusData.list;

    // 获取种数据
    const { data: strainData } = await baseService.get("/bga/phytostrain/page", {
      page: 1,
      limit: 99999,
    });
    phytostrain.value = strainData.list;
  });

  const Card_Style_Header = ref({
    fontSize: "2em",
    display: "flex",
    "justify-content": "center",
    "align-items": "center",
  });
  const Card_Style_Body = ref({
    cursor: "pointer",
    fontSize: "1.2em",
    display: "flex",
    "justify-content": "center",
    "align-items": "center",
  });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };

  // 请求
  const fetchData = (nameCn: string) => baseService.get(`/bga/phytodetail/page`);
</script>

<style scoped lang="less">
  @import "../../assets/theme/base.less";
  .phyto {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    min-width: 150px;
    max-width: 250px;

    .el-card {
      margin-bottom: 5%;
      &.first {
        border-bottom: 5px solid @light-text-active !important;
      }
    }
  }

  .el-scrollbar__view .el-card:hover {
    color: @light-text-active;
  }

  .el-scrollbar__view .el-card.active {
    background: @light-bg-active;
    border: 1px solid @light-text-active;
    color: @light-text-active;
  }

  .el-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .el-col {
    flex: 1;
    margin: 0 10px;
    min-width: 150px;
  }
</style>
