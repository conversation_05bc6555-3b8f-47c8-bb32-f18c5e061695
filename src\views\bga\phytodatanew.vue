<template>
  <div class="mod-bga__phytodatanew">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-button type="primary" @click="uploadBgaDataHandle()">{{ $t("phytoplankto.uploadDataTable") }}</el-button>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="downloadFile">下载模板</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">{{ $t("导入Excel数据至数据库") }}</el-button>
      </el-form-item>
      <el-upload action="bga/phytodatanew/excel-input" class="upload-demo" name="file" :limit="1" ref="upload" :auto-upload="false" accept=".xlsx,.xls,.xlsm" :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
    </el-form>
    <!-- 弹窗, 新增 / 修改 -->
    <div style="display: flex; justify-content: left; align-items: start">
      <!-- <div style="display: flex; justify-content: left; align-items: center">
        <div style="margin-top: 100px">
          <el-space direction="vertical" alignment="start" :size="30">
            <el-radio-group v-model="radio1" v-for="item in years" :key="item.id">
              <el-radio-button :label="item" class="month-card" @change="search" />
            </el-radio-group>
          </el-space>
        </div>
      </div> -->
      <el-tree style="max-width: 300px; min-width: 300px" :data="treeData" :props="defaultProps" accordion @node-click="handleNodeClick" />
      <el-main>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" style="margin-top: 20px">
          <el-form-item>
            <el-form-item>
              <el-input style="width: 200px" v-model="state.dataForm.stationName" placeholder="测站名称" clearable></el-input>
            </el-form-item>
            <!--时间查询-->
            <el-form-item label="开始时间">
              <el-date-picker v-model="state.dataForm.startTime" type="datetime" placeholder="选择开始时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker v-model="state.dataForm.endTime" type="datetime" placeholder="选择结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
            </el-form-item>
            <!--时间查询-->
            <el-form-item>
              <el-button @click="search">{{ $t("query") }}</el-button>
            </el-form-item>
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="info" @click="state.exportHandle()">导出</el-button>
          </el-form-item>
        </el-form>
        <div v-if="state.dataList && state.dataList.length > 0">
          <!-- 固定表头并将站点名称列固定 -->
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="samplingId" :label="$t('phytoplankto.test1')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="stationId" :label="$t('phytoplankto.test2')" header-align="center" align="center"></el-table-column>
            <el-table-column width="220" prop="stationName" :label="$t('phytoplankto.test3')" header-align="center" align="center" fixed="left"></el-table-column>
            <el-table-column prop="timeDay" :label="$t('phytoplankto.test6')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="timeDetail" :label="$t('phytoplankto.test7')" header-align="center" align="center"></el-table-column>

            <el-table-column prop="phytoPhylum" :label="$t('phytoplankto.test13')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="phytoCategory" :label="$t('属/种类')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="phytoLatin" :label="$t('phytoplankto.test19')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="wetWeight" :label="$t('phytoplankto.test20')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="phytoNumber" :label="$t('phytoplankto.test21')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="cellNumber" :label="$t('phytoplankto.test22')" header-align="center" align="center"></el-table-column>
            <el-table-column prop="biomass" :label="$t('phytoplankto.test23')" header-align="center" align="center"></el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        </div>
        <el-container style="justify-content: center" v-if="state.dataList && state.dataList.length === 0"> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
      </el-main>
    </div>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, onBeforeUnmount } from "vue";
// 兼容部分组件没有 default export，使用命名空间导入
import AddOrUpdate from "./phytodatanew-add-or-update.vue";
import { ElLoading } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router"; // 导入 useRoute
const { $t } = globalLanguage();
const route = useRoute();
const condition = ref<any>(null);
const year_now = ref<any>(new Date().getFullYear());
const years = reactive<any>([]);
interface Tree {
  label: string;
  children?: Tree[];
}

const handleNodeClick = (data: any, getNode: any) => {
  const month = getNode.parent.label;
  const year = month ? getNode.parent.parent.label : undefined;
  if (getNode.data.children.length == 0) {
    console.log("查询", year, month, data.label);
    if (year === undefined && month === undefined) {
      state.dataForm.year = data.label;
      state.getDataList();
      return;
    }
    if (year === undefined) {
      state.dataForm.year = month;
      state.dataForm.month = data.label;
      state.getDataList();
      return;
    }
    state.dataForm.year = year;
    state.dataForm.month = month;
    state.dataForm.stationName = data.label;
    state.getDataList();
  }
};
const treeData = reactive<Tree[]>([]); // null>([]); // 定义一个响应式变量用于存储树形数据

// 获取树形数据
const getTreeData = () => {
  baseService.get("/bga/condition/list", { limit: 100000 }).then((res) => {
    // 假设 res.data 是服务器返回的数据
    const data = res.data.map((item: any) => ({
      label: item.label,
      children: item.children || [] // 确保 children 是一个数组
    }));
    //先清空treeData数组
    treeData.splice(0, treeData.length);
    // 将获取到的数据赋值给 treeData
    treeData.push(...data); // 使用 push 将元素添加到响应式数组中
  });
};
// 获取月份数据
getTreeData();
const defaultProps = {
  children: "children",
  label: "label"
};
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/phytodatanew/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytodatanew/export",
  deleteURL: "/bga/phytodatanew",
  uploadURL: "/bga/phytodatanew/upload",
  dataForm: {
    year: "",
    month: "",
    stationName: "",
    stationId: "",
    startTime: "", // 开始时间
    endTime: "" // 结束时间
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

const search = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

// 表格高度计算（用于固定表头）
const tableHeight = ref<number>(400);
const calculateTableHeight = () => {
  const offset = 260; // 根据页面顶部控件和分页预留高度，按需调整
  const h = window.innerHeight - offset;
  tableHeight.value = h > 200 ? h : 200;
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

const state_excel = reactive({
  selectedFile: null
});

function handleFileChange(file: any, fileList: any) {
  // 当用户选择文件时，更新selectedFile
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw; // 使用 fileList[0].raw 获取原始 File 对象
  } else {
    state_excel.selectedFile = null; // 如果没有文件，则设置为 null
  }
}

function handleFileUpload() {
  //判断state_excel.selectedFile是否有值
  if (!state_excel.selectedFile) {
    ElMessage.error("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  openLoading();
  baseService
    .post("/bga/phytodatanew/excel-input", formData)
    .then((response) => {
      ElMessage.success("上传成功");
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    })
    .catch((error) => {
      console.error("错误:", error);
      ElMessage.success("上传成功");
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    });
}
const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};
const closeLoading = () => {
  ElLoading.service().close();
};
const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/PhytoExcelDataImport.xlsx"; // 文件相对于 public 文件夹的路径
  downloadLink.download = "浮游植物Excel数据表模板.xlsx"; // 下载时的文件名

  // 模拟点击下载链接
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};
</script>

<style scoped>
.el-radio-button {
  --el-radio-button-checked-bg-color: #edf9f8;
  --el-radio-button-checked-text-color: #17b3a3;
}
::v-deep .month-card .el-radio-button__inner {
  width: 300px;
  height: 60px;
  font-size: 18px;
  padding-top: 20px;
}
</style>
