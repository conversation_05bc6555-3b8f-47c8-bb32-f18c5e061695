<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('zooplankton.nameCn')" prop="nameCn">
        <el-input v-model="dataForm.nameCn" :placeholder="$t('zooplankton.nameCn')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.nameLatin')" prop="nameLatin">
        <el-input v-model="dataForm.nameLatin" :placeholder="$t('zooplankton.nameLatin')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('zooplankton.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.updateAt')" prop="updateAt">
        <el-input v-model="dataForm.updateAt" :placeholder="$t('zooplankton.updateAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.deleteAt')" prop="deleteAt">
        <el-input v-model="dataForm.deleteAt" :placeholder="$t('zooplankton.deleteAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('zooplankton.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  nameCn: "",
  nameLatin: "",
  remark: "",
  creator: "",
  updater: "",
  createdAt: "",
  updateAt: "",
  deleteAt: ""
});

const rules = ref({
  nameCn: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nameLatin: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/zooplankton/zptkind/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/zooplankton/zptkind", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
