<template>
  <div class="mod-bga__zptcount">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptcount:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptcount:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.zptId')" prop="zptId" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.zptCategoryName')" prop="zptCategoryName" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.zptGenusName')" prop="zptGenusName" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.stationCode')" prop="stationCode" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.stationName')" prop="stationName" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.zptCount')" prop="zptCount" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.createdAt')" prop="createdAt" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.updateAt')" prop="updateAt" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.deleteAt')" prop="deleteAt" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" :label="$t('zooplankton.remark')" prop="remark" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('bga:zptcount:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('bga:zptcount:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zptcount-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/zooplankton/zptcount/page",
  getDataListIsPage: true,
  exportURL: "/zooplankton/zptcount/export",
  deleteURL: "/zooplankton/zptcount"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
