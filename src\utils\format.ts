export function formatTimestamp(time: string) {
  let timestamp = Date.parse(time);
  // 创建一个 Date 对象并将时间戳传递给它
  const date = new Date(timestamp);
  // 使用 Date 对象的方法获取北京时间
  // 中国（北京）的时区偏移为 UTC+8，所以我们需要加上 8 小时
  const beijingTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
  // 格式化北京时间为字符串 "YYYY-MM-DD HH:mm:ss"
  const formattedBeijingTime = beijingTime.toISOString().slice(0, 19).replace("T", " ");
  return formattedBeijingTime; // 输出北京时间
}

export function formatDate() {
  const nowTime = new Date();
  const year = nowTime.getFullYear();
  let month = (nowTime.getMonth() + 1).toString().padStart(2, "0");
  let day = nowTime.getDate().toString().padStart(2, "0");
  let hours = nowTime.getHours().toString().padStart(2, "0");
  let minutes = nowTime.getMinutes().toString().padStart(2, "0");
  let seconds = nowTime.getSeconds().toString().padStart(2, "0");
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedTime; // 输出指定格式的时间字符串
}
