<template>
  <div class="mod-bga__taddress">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="Id" header-align="center" align="center"></el-table-column>
      <el-table-column prop="address" :label="$t('bga.address')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="latitude" :label="$t('bga.latitude')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="longitude" :label="$t('bga.longitude')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="memo" :label="$t('bga.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="creator" :label="$t('bga.creator')" header-align="center" align="center"></el-table-column>
      <el-table-column width="160" prop="createDate" :label="$t('bga.createDate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="160" prop="updateDate" :label="$t('bga.updateDate')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./taddress-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/taddress/page",
  getDataListIsPage: true,
  exportURL: "/bga/taddress/export",
  deleteURL: "/bga/taddress"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
