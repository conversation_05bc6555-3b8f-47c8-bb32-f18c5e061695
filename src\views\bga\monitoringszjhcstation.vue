<template>
  <div class="mod-bga__monitoringszjhcstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.stnm" :placeholder="$t('测站名称')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.location" :placeholder="$t('所属流域')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.atst" :placeholder="$t('是否为自动监测(是或否)')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t('delete') }}</el-button>
      </el-form-item>
    </el-form>
  <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" :height="tableHeight" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="wystcd" :label="$t('monitoring.wystcd')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stcd" :label="$t('monitoring.stcd')" header-align="center" align="center"></el-table-column>
  <el-table-column width="150" prop="stnm" :label="$t('monitoring.stnm')" header-align="center" align="center" fixed="left">
        <template v-slot="scope">
          <el-button type="primary" link @click="handleStationClick(scope.row)">
            {{ scope.row.stnm }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="150" prop="location" :label="$t('所属流域')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="eslo" :label="$t('monitoring.eslo')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ntla" :label="$t('monitoring.ntla')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="munit" :label="$t('monitoring.munit')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="msunit" :label="$t('monitoring.msunit')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="atst" :label="$t('monitoring.atst')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stlvl" :label="$t('monitoring.stlvl')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="bnnm" :label="$t('monitoring.bnnm')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="rvnm" :label="$t('monitoring.rvnm')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="staddr" :label="$t('monitoring.staddr')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="adcd" :label="$t('monitoring.adcd')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="wudcd" :label="$t('monitoring.wudcd')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="mnfrq" :label="$t('monitoring.mnfrq')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="fndym" :label="$t('monitoring.fndym')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="endym" :label="$t('monitoring.endym')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remarks" :label="$t('monitoring.remarks')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs, onMounted, onBeforeUnmount } from "vue";
  import AddOrUpdate from "./monitoringszjhcstation-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import { useRouter } from "vue-router";
  const { $t } = globalLanguage();
  const router = useRouter();

  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/monitoringszjhcstation/page",
    getDataListIsPage: true,
    exportURL: "/bga/monitoringszjhcstation/export",
    deleteURL: "/bga/monitoringszjhcstation",
    dataForm:{
    stnm:"",
    location:"",
    atst:"",
    }
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };

  // 表格高度自动计算（用于固定表头）
  const tableHeight = ref<number>(400);

  function calculateTableHeight() {
    const offset = 200; // 估算顶部控件与分页占用高度
    tableHeight.value = Math.max(window.innerHeight - offset, 200);
  }

  onMounted(() => {
    calculateTableHeight();
    window.addEventListener("resize", calculateTableHeight);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", calculateTableHeight);
  });

  // 根据remarks字段跳转到不同页面
  const handleStationClick = (row: any) => {
    const { remarks, stnm } = row;
    console.log('点击测站名称，remarks:', remarks, 'stnm:', stnm);
    switch (remarks) {
      case '1':
        // 跳转至wqyzw页面，参数名为station
        router.push({
          path: '/waterquality/wqyzw',
          query: { station: stnm, from: 'monitoring' }
        });
        break;
      case '2':
        // 跳转至interface-data-szjhc-auto页面，参数名为stationName
        router.push({
          path: '/task/interface-data-szjhc-auto',
          query: { stationName: stnm, from: 'monitoring' }
        });
        break;
      case '3':
        // 跳转至interface-data-hedao-stbc-auto页面，参数名为stationName
        router.push({
          path: '/task/interface-data-hedao-stbc-auto',
          query: { stationName: stnm, from: 'monitoring' }
        });
        break;
      case '4':
        // 跳转至interface-data-hedao-guokong-auto页面，参数名为stationName
        router.push({
          path: '/task/interface-data-hedao-guokong-auto',
          query: { stationName: stnm, from: 'monitoring' }
        });
        break;
      case '5':
        // 跳转至interface-test页面，参数名为stationName
        router.push({
          path: '/bga/interface-test',
          query: { stationName: stnm, from: 'monitoring' }
        });
        break;
        case '6':
        // 跳转至interface-data-huti-guocai-auto页面，参数名为stationName
        router.push({
          path: '/task/interface-data-huti-guocai-auto',
          query: { stationName: stnm, from: 'monitoring' }
        });
        break;
      default:
        // 默认不跳转或提示信息
        break;
    }
  };
</script>
