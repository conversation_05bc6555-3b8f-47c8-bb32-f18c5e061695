<template>
  <div class="line-chart-container">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <span class="unit">{{ unit }}</span>
    </div>
    <div class="chart-wrapper">
      <div ref="chartEl" style="width: 100%; height: 400px"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from "vue";
import * as echarts from "echarts";
import type { ECharts } from "echarts";

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      dates: [],
      values: []
    })
  },
  title: {
    type: String,
    default: "数据趋势"
  },
  unit: {
    type: String,
    default: ""
  }
});

const chartEl = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 初始化图表
function initChart() {
  if (!chartEl.value) return;

  chartInstance = echarts.init(chartEl.value);
  updateChart();
}

// 更新图表
function updateChart() {
  if (!chartInstance) return;

  const option = {
    tooltip: {
      trigger: "axis",
      formatter: (params: any) => {
        return `${params[0].axisValue}<br/>${params[0].marker} ${props.title}: ${params[0].data} ${props.unit}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: "value",
      name: props.unit,
      nameLocation: "end"
    },
    series: [
      {
        name: props.title,
        type: "line",
        data: props.chartData.values,
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: "#409EFF"
        },
        lineStyle: {
          width: 3
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(64, 158, 255, 0.5)"
            },
            {
              offset: 1,
              color: "rgba(64, 158, 255, 0.1)"
            }
          ])
        }
      }
    ]
  };

  chartInstance.setOption(option);
}

// 监听窗口大小变化
function handleResize() {
  if (chartInstance) {
    chartInstance.resize();
  }
}

function resize() {
  if (chartInstance) {
    chartInstance.resize({
      width: "auto",
      height: "auto",
      animation: {
        duration: 300, // 动画持续时间300ms
      }
    });
  }
}

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
});

// 暴露方法给父组件
defineExpose({
  updateChart,
  resize
});
</script>

<style scoped lang="less">
.line-chart-container {
  width: 100%;
  height: 100%;

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: normal;
    }

    .unit {
      color: #999;
      font-size: 14px;
    }
  }

  .chart-wrapper {
    width: 100%;
  }
}
</style>
