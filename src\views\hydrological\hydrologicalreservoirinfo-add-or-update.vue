<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('hydrological.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('hydrological.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.waterTemperature')" prop="waterTemperature">
        <el-input v-model="dataForm.waterTemperature" :placeholder="$t('hydrological.waterTemperature')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.waterLevel')" prop="waterLevel">
        <el-input v-model="dataForm.waterLevel" :placeholder="$t('hydrological.waterLevel')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.reservoirCapacity')" prop="reservoirCapacity">
        <el-input v-model="dataForm.reservoirCapacity" :placeholder="$t('hydrological.reservoirCapacity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.inflowRate')" prop="inflowRate">
        <el-input v-model="dataForm.inflowRate" :placeholder="$t('hydrological.inflowRate')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.outflowRate')" prop="outflowRate">
        <el-input v-model="dataForm.outflowRate" :placeholder="$t('hydrological.outflowRate')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.flowMeasMethod')" prop="flowMeasMethod">
        <el-input v-model="dataForm.flowMeasMethod" :placeholder="$t('hydrological.flowMeasMethod')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.waterDepth')" prop="waterDepth">
        <el-input v-model="dataForm.waterDepth" :placeholder="$t('hydrological.waterDepth')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('hydrological.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('hydrological.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('hydrological.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationCode: "",
  createdAt: "",
  waterTemperature: "",
  waterLevel: "",
  reservoirCapacity: "",
  inflowRate: "",
  outflowRate: "",
  flowMeasMethod: "",
  waterDepth: "",
  remark: ""
});

const rules = ref({
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterTemperature: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterLevel: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  reservoirCapacity: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  inflowRate: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  outflowRate: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  flowMeasMethod: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterDepth: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/hydrological/hydrologicalreservoirinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/hydrological/hydrologicalreservoirinfo", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
