<template>
  <el-dialog v-model="visible" align-center :title="title" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('zooplankton.sampleDataId')" prop="stationId">
        <el-input :disabled="disabled" v-model="dataForm.stationId" :placeholder="$t('zooplankton.sampleDataId')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.samplingLocation')" prop="samplingLocation">
        <el-input :disabled="disabled" v-model="dataForm.samplingLocation" :placeholder="$t('zooplankton.samplingLocation')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.sampleVolumeLiters')" prop="sampleVolumeLiters">
        <el-input :disabled="disabled" v-model="dataForm.sampleVolumeLiters" :placeholder="$t('zooplankton.sampleVolumeLiters')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.concentratedVolume')" prop="concentratedVolume">
        <el-input :disabled="disabled" v-model="dataForm.concentratedVolume" :placeholder="$t('zooplankton.concentratedVolume')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.dilutionFactor')" prop="dilutionFactor">
        <el-input :disabled="disabled" v-model="dataForm.dilutionFactor" :placeholder="$t('zooplankton.dilutionFactor')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.fieldOfViewCount')" prop="fieldOfViewCount">
        <el-input :disabled="disabled" v-model="dataForm.fieldOfViewCount" :placeholder="$t('zooplankton.fieldOfViewCount')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.fieldArea')" prop="fieldArea">
        <el-input :disabled="disabled" v-model="dataForm.fieldArea" :placeholder="$t('zooplankton.fieldArea')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.countingArea')" prop="countingArea">
        <el-input :disabled="disabled" v-model="dataForm.countingArea" :placeholder="$t('zooplankton.countingArea')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.countingFrameVolume')" prop="countingFrameVolume">
        <el-input :disabled="disabled" v-model="dataForm.countingFrameVolume" :placeholder="$t('zooplankton.countingFrameVolume')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.countingFrameArea')" prop="countingFrameArea">
        <el-input :disabled="disabled" v-model="dataForm.countingFrameArea" :placeholder="$t('zooplankton.countingFrameArea')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.samplingAt')" prop="samplingAt">
        <el-input :disabled="disabled" v-model="dataForm.samplingAt" :placeholder="$t('zooplankton.samplingAt')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer v-if="!disabled">
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const disabled = ref(false);
const visible = ref(false);
const dataFormRef = ref();
const title = ref("");
const dataForm = reactive({
  id: "",
  stationId: "",
  samplingLocation: "",
  sampleVolumeLiters: "",
  concentratedVolume: "",
  dilutionFactor: "",
  fieldOfViewCount: "",
  fieldArea: "",
  countingArea: "",
  countingFrameVolume: "",
  countingFrameArea: "",
  samplingAt: ""
});

const rules = ref({
  stationId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingLocation: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id: number, type: string) => {
  title.value = type === "detail" ? $t("details") : $t("update");
  disabled.value = type === "detail" ? true : false;
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/zooplankton/zptsampledata/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/zooplankton/zptsampledata", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
