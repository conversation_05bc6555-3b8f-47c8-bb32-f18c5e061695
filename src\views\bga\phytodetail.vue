<template>
  <div class="mod-bga__phytodetail">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.type" :placeholder="$t('种类名称')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.contributor" :placeholder="$t('申请人/贡献者')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 200px" v-model="state.dataForm.status" :placeholder="$t('数据状态')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()"> {{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="name" :label="$t('phytoplankto.name')" header-align="center" align="center"></el-table-column>

      <!-- 使用 formatter 函数将枚举值转换为描述文字 -->
      <el-table-column width="150" prop="type" :label="$t('phytoplankto.type')" header-align="center" align="center">
        <template v-slot="scope">
          {{ formatValue(scope.row.type, typeMap) }}
        </template>
      </el-table-column>

      <el-table-column width="150" prop="identification" :label="$t('phytoplankto.identification')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationAddr" :label="$t('phytoplankto.stationAddr')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="autecology" :label="$t('phytoplankto.autecology')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="contributor" :label="$t('phytoplankto.contributor')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="reviewer" :label="$t('phytoplankto.reviewer')" header-align="center" align="center"></el-table-column>

      <!-- 使用 formatter 函数将枚举值转换为描述文字 -->
      <el-table-column width="150" prop="status" :label="$t('phytoplankto.status')" header-align="center" align="center">
        <template v-slot="scope">
          {{ formatValue(scope.row.status, statusMap) }}
        </template>
      </el-table-column>

      <el-table-column width="150" prop="description" :label="$t('phytoplankto.description')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('bga.remark')" header-align="center" align="center"></el-table-column>

      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="viewDetailHandle(scope.row.id)">{{ $t("查看") }}</el-button>
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <!-- 弹窗, 查看详情 -->
    <detail-view ref="detailViewRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</detail-view>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./phytodetail-add-or-update.vue";
  import DetailView from "./phytodetail-View.vue"; // 引入查看详情的弹窗组件
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();

  // 定义映射对象
  const typeMap = {
    0: $t("0:初次提交审核"),
    1: $t("1:审核通过"),
    2: $t("2:驳回"),
  };

  const statusMap = {
    0: $t("0:初次提交审核"),
    1: $t("1:审核通过"),
    2: $t("2:驳回"),
    // 添加更多映射
  };

  // 格式化函数
const formatValue = (value: string, map: Record<string, string>) => {
  return map[value] || value || "";
};

  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/phytodetail/page",
    getDataListIsPage: true,
    exportURL: "/bga/phytodetail/export",
    deleteURL: "/bga/phytodetail",
    dataForm: {
      type: "",
      contributor: "",
      status: "",
  }
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });
  const addOrUpdateRef = ref();
  const detailViewRef = ref(); // 引入查看详情的弹窗组件引用

  // 新增/修改
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };

  // 查看详情
  const viewDetailHandle = (id: number) => {
    detailViewRef.value.init(id);
  };
</script>
