<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('waterquality.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('waterquality.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.verticalLineNum')" prop="verticalLineNum">
        <el-input v-model="dataForm.verticalLineNum" :placeholder="$t('waterquality.verticalLineNum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.layerNum')" prop="layerNum">
        <el-input v-model="dataForm.layerNum" :placeholder="$t('waterquality.layerNum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.totalHardness')" prop="totalHardness">
        <el-input v-model="dataForm.totalHardness" :placeholder="$t('waterquality.totalHardness')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.totalAlkalinity')" prop="totalAlkalinity">
        <el-input v-model="dataForm.totalAlkalinity" :placeholder="$t('waterquality.totalAlkalinity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.totalOrganicCarbon')" prop="totalOrganicCarbon">
        <el-input v-model="dataForm.totalOrganicCarbon" :placeholder="$t('waterquality.totalOrganicCarbon')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.dissolvedOrganicCarbon')" prop="dissolvedOrganicCarbon">
        <el-input v-model="dataForm.dissolvedOrganicCarbon" :placeholder="$t('waterquality.dissolvedOrganicCarbon')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.chemicalOxygenDemand')" prop="chemicalOxygenDemand">
        <el-input v-model="dataForm.chemicalOxygenDemand" :placeholder="$t('waterquality.chemicalOxygenDemand')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.totalPhosphorus')" prop="totalPhosphorus">
        <el-input v-model="dataForm.totalPhosphorus" :placeholder="$t('waterquality.totalPhosphorus')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.solublePhosphate')" prop="solublePhosphate">
        <el-input v-model="dataForm.solublePhosphate" :placeholder="$t('waterquality.solublePhosphate')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.totalNitrogen')" prop="totalNitrogen">
        <el-input v-model="dataForm.totalNitrogen" :placeholder="$t('waterquality.totalNitrogen')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.ammoniaNitrogen')" prop="ammoniaNitrogen">
        <el-input v-model="dataForm.ammoniaNitrogen" :placeholder="$t('waterquality.ammoniaNitrogen')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.nitrite')" prop="nitrite">
        <el-input v-model="dataForm.nitrite" :placeholder="$t('waterquality.nitrite')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.nitrate')" prop="nitrate">
        <el-input v-model="dataForm.nitrate" :placeholder="$t('waterquality.nitrate')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('waterquality.permanganateIndex')" prop="permanganateIndex">
        <el-input v-model="dataForm.permanganateIndex" :placeholder="$t('waterquality.permanganateIndex')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.bod5')" prop="bod5">
        <el-input v-model="dataForm.bod5" :placeholder="$t('waterquality.bod5')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.copper')" prop="copper">
        <el-input v-model="dataForm.copper" :placeholder="$t('waterquality.copper')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.zinc')" prop="zinc">
        <el-input v-model="dataForm.zinc" :placeholder="$t('waterquality.zinc')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.arsenic')" prop="arsenic">
        <el-input v-model="dataForm.arsenic" :placeholder="$t('waterquality.arsenic')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.mercury')" prop="mercury">
        <el-input v-model="dataForm.mercury" :placeholder="$t('waterquality.mercury')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.cadmium')" prop="cadmium">
        <el-input v-model="dataForm.cadmium" :placeholder="$t('waterquality.cadmium')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.hexavalentChromium')" prop="hexavalentChromium">
        <el-input v-model="dataForm.hexavalentChromium" :placeholder="$t('waterquality.hexavalentChromium')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.lead')" prop="lead">
        <el-input v-model="dataForm.lead" :placeholder="$t('waterquality.lead')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.iron')" prop="iron">
        <el-input v-model="dataForm.iron" :placeholder="$t('waterquality.iron')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.manganese')" prop="manganese">
        <el-input v-model="dataForm.manganese" :placeholder="$t('waterquality.manganese')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.nickel')" prop="nickel">
        <el-input v-model="dataForm.nickel" :placeholder="$t('waterquality.nickel')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.selenium')" prop="selenium">
        <el-input v-model="dataForm.selenium" :placeholder="$t('waterquality.selenium')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.fluoride')" prop="fluoride">
        <el-input v-model="dataForm.fluoride" :placeholder="$t('waterquality.fluoride')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.totalCyanide')" prop="totalCyanide">
        <el-input v-model="dataForm.totalCyanide" :placeholder="$t('waterquality.totalCyanide')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.sulfide')" prop="sulfide">
        <el-input v-model="dataForm.sulfide" :placeholder="$t('waterquality.sulfide')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.chloride')" prop="chloride">
        <el-input v-model="dataForm.chloride" :placeholder="$t('waterquality.chloride')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.petroleum')" prop="petroleum">
        <el-input v-model="dataForm.petroleum" :placeholder="$t('waterquality.petroleum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.sulfate')" prop="sulfate">
        <el-input v-model="dataForm.sulfate" :placeholder="$t('waterquality.sulfate')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.anionicSurfactant')" prop="anionicSurfactant">
        <el-input v-model="dataForm.anionicSurfactant" :placeholder="$t('waterquality.anionicSurfactant')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.pcbs')" prop="pcbs">
        <el-input v-model="dataForm.pcbs" :placeholder="$t('waterquality.pcbs')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.pahs')" prop="pahs">
        <el-input v-model="dataForm.pahs" :placeholder="$t('waterquality.pahs')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.volatilePhenols')" prop="volatilePhenols">
        <el-input v-model="dataForm.volatilePhenols" :placeholder="$t('waterquality.volatilePhenols')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.fecalColiforms')" prop="fecalColiforms">
        <el-input v-model="dataForm.fecalColiforms" :placeholder="$t('waterquality.fecalColiforms')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.microcystinToxin')" prop="microcystinToxin">
        <el-input v-model="dataForm.microcystinToxin" :placeholder="$t('waterquality.microcystinToxin')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.chlorophyllA')" prop="chlorophyllA">
        <el-input v-model="dataForm.chlorophyllA" :placeholder="$t('waterquality.chlorophyllA')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('waterquality.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('waterquality.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationCode: "",
  verticalLineNum: "",
  layerNum: "",
  createdAt: "",
  totalHardness: "",
  totalAlkalinity: "",
  totalOrganicCarbon: "",
  dissolvedOrganicCarbon: "",
  chemicalOxygenDemand: "",
  totalPhosphorus: "",
  solublePhosphate: "",
  totalNitrogen: "",
  ammoniaNitrogen: "",
  nitrite: "",
  nitrate: "",
  permanganateIndex: "",
  bod5: "",
  copper: "",
  zinc: "",
  arsenic: "",
  mercury: "",
  cadmium: "",
  hexavalentChromium: "",
  lead: "",
  iron: "",
  manganese: "",
  nickel: "",
  selenium: "",
  fluoride: "",
  totalCyanide: "",
  sulfide: "",
  chloride: "",
  petroleum: "",
  sulfate: "",
  anionicSurfactant: "",
  pcbs: "",
  pahs: "",
  volatilePhenols: "",
  fecalColiforms: "",
  microcystinToxin: "",
  chlorophyllA: "",
  remark: ""
});

const rules = ref({
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  verticalLineNum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  layerNum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalHardness: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalAlkalinity: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalOrganicCarbon: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  dissolvedOrganicCarbon: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  chemicalOxygenDemand: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalPhosphorus: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  solublePhosphate: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalNitrogen: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  ammoniaNitrogen: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nitrite: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nitrate: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  permanganateIndex: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  bod5: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  copper: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zinc: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  arsenic: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  mercury: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  cadmium: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  hexavalentChromium: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  lead: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  iron: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  manganese: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  nickel: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  selenium: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  fluoride: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  totalCyanide: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  sulfide: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  chloride: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  petroleum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  sulfate: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  anionicSurfactant: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  pcbs: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  pahs: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  volatilePhenols: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  fecalColiforms: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  microcystinToxin: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  chlorophyllA: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/waterquality/wqchemicalinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/waterquality/wqchemicalinfo", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
