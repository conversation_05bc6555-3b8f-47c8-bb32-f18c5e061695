import app from "@/constants/app";
import { IHttpResponse, IObject } from "@/types/interface";
import router from "@/router";
import axios, { AxiosRequestConfig } from "axios";
import qs from "qs";
import { getToken } from "./cache";
import { getValueByKeys } from "./utils";
import { ElMessage } from "element-plus";
import error from "@/views/error.vue";

const http = axios.create({
  baseURL: app.api,
  timeout: app.requestTimeout
});

http.interceptors.request.use(
  function (config: any) {
    config.headers["X-Requested-With"] = "XMLHttpRequest";
    config.headers["Request-Start"] = new Date().getTime();
    const token = getToken();
    if (token) {
      config.headers["token"] = token;
    }
    if (config.method?.toUpperCase() === "GET") {
      config.params = { ...config.params, _t: new Date().getTime() };
    }
    if (Object.values(config.headers).includes("application/x-www-form-urlencoded")) {
      config.data = qs.stringify(config.data);
    }
    console.log(config);
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);
http.interceptors.response.use(
  (response : any) => {
    // 响应成功
    if (response.data.code === 0) {
      return response;
    }

    // 错误提示
    ElMessage.error(response.data.msg);

    if (response.data.code === 401) {
      //自定义业务状态码
      redirectLogin();
    }
    console.log(response)
    console.log(response.data)
    console.log(response.data.msg);
    return Promise.reject(new Error(response.data.msg)).catch(e=>{console.log(e)});
    //return Promise.reject(new Error(response.data.msg || "Error"));
  },
  (error) => {
    const status = getValueByKeys(error, "response.status", 500);
    const httpCodeLabel: IObject<string> = {
      400: "请求参数错误",
      401: "未授权，请登录",
      403: "拒绝访问",
      404: `请求地址出错: ${getValueByKeys(error, "response.config.url", "")}`,
      408: "请求超时",
      500: "API接口报500错误",
      501: "服务未实现",
      502: "网关错误",
      503: "服务不可用",
      504: "网关超时",
      505: "HTTP版本不受支持"
    };
    if (error && error.response) {
      console.error("请求错误", error.response.data);
    }
    if (status === 401) {
      redirectLogin();
    }
    return Promise.reject(new Error(httpCodeLabel[status] || "接口错误"));
  }

);

const redirectLogin = () => {
  router.replace("/login");
  return;
};

/*
export default (o: AxiosRequestConfig): Promise<IHttpResponse> => {
  return new Promise((resolve, reject) => {
    o.baseURL="http://localhost:9080";
    o.url="http://localhost:9080/bga/taddress/page";
    http(o)
      .then((res) =>
      {
        if (res && res.data)
        {
          return resolve(res.data);
        }
        else {
          // 处理 res 为 undefined 或 res.data 不存在的情况
          console.log(res);
          console.log(res.data);
          reject(new Error('No data received from API'));
        }
      })
      //.catch(reject);
      .catch((e) => {
        console.error('Error occurred:', e); // 使用 console.error 以便在日志中更清晰地识别错误
        reject(e); // 将错误传播到外部
      });
  });
};
*/


export default async (o: AxiosRequestConfig): Promise<IHttpResponse> => {
  try {
    // o.baseURL="/api";
    //o.url="http://localhost:9080/home";
    const res = await http(o);
    if (res !== undefined && res.data !== undefined) {
      return res.data;
    } else {
      throw new Error('Invalid response from API');
    }
  } catch (e) {
    console.error('Error occurred during API call:', e);
    throw e; // 重新抛出错误以便调用者可以捕获
  }
};


