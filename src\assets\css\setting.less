.rr-setting {
  padding: 20px;
  .el-divider {
    margin: 20px 0;
  }
  &-wrap {
    .el-drawer__header {
      color: #595959;
      font-size: 15px;
      margin-bottom: 0;
      padding: 13px 16px;
      border-bottom: 1px solid #f4f4f4;
    }
    .el-drawer__body {
      overflow: auto;
      padding: 0;
    }
  }
  &-title {
    font-size: 13px;
  }
  // 主题
  .rr-theme {
    .card {
      width: 50px;
      height: 35px;
      border-radius: 3px;
      margin: 0 20px 20px 0;
      background-color: #f5f7fa;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
      display: inline-block;
      vertical-align: top;
      position: relative;
      cursor: pointer;
      //侧边栏
      &.side {
        &::before {
          content: "";
          width: 15px;
          height: 100%;
          background-color: #fff;
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
          display: inline-block;
          vertical-align: top;
        }
        &.dark {
          &::before {
            background-color: #2e3549;
          }
        }
      }
      //顶栏
      &.header {
        &::before {
          content: "";
          border-top-left-radius: 3px;
          border-bottom-left-radius: 3px;
          display: inline-block;
          vertical-align: top;
          width: 100%;
          height: 10px;
          background-color: #fff;
          border-bottom-left-radius: 0;
          border-top-right-radius: 3px;
        }
        &.light {
          &::before {
            width: 100%;
            height: 10px;
            background-color: #fff;
            border-bottom-left-radius: 0;
            border-top-right-radius: 3px;
          }
        }
        &.dark {
          &::before {
            background-color: #2e3549;
          }
        }
        &.primary {
          &::before {
            background-color: #409eff;
          }
        }
      }

      &.mix {
        background-color: #2e3549;
        &.dark {
          &::before {
            background-color: #f0f2f5;
            width: 35px;
            height: 25px;
            position: absolute;
            bottom: 0;
            right: 0;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 3px;
          }
        }
      }

      &.side,
      &.header,
      &.mix {
        &.active {
          &::after {
            content: "";
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #19be6b;
            position: absolute;
            left: 50%;
            bottom: -15px;
            margin-left: -3px;
          }
        }
      }
    }
    //主色调
    .color {
      width: 20px;
      height: 20px;
      margin: 8px 8px 0 0;
      border-radius: 2px;
      display: inline-block;
      box-shadow: 0 1px 3px rgba(0 0 0, 0.1);
      vertical-align: top;
      position: relative;
      cursor: pointer;
      &.active {
        &::after {
          content: url('data:image/svg+xml;charset=utf-8,<svg width="14" height="14" color="rgb(255 255 255)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M406.656 706.944L195.84 496.256a32 32 0 10-45.248 45.248l256 256 512-512a32 32 0 00-45.248-45.248L406.592 706.944z"></path></svg>');
          font-family: element-icons !important;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          position: absolute;
          top: 50%;
          left: 50%;
          margin: -7px 0 0 -7px;
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
  .rr-theme,
  .rr-other {
    width: 100%;
    > .el-space__item {
      width: 100%;
    }
  }
  .rr-switch {
    justify-content: space-between;
    width: 100%;
  }
}
