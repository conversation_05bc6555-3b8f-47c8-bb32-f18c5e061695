<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('日期')" prop="time">
        <el-input v-model="dataForm.time" :placeholder="$t('日期')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('水华面积')" prop="area">
        <el-input v-model="dataForm.area" :placeholder="$t('水华面积')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('占湖比')" prop="occupy">
        <el-input v-model="dataForm.occupy" :placeholder="$t('占湖比')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('水华等级')" prop="equivalent">
        <el-input v-model="dataForm.equivalent" :placeholder="$t('水华等级')"></el-input>
      </el-form-item><el-form-item :label="$t('分布区域')" prop="distribution">
      <el-input v-model="dataForm.distribution" :placeholder="$t('分布区域')"></el-input>
    </el-form-item>

    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const emit = defineEmits(["refreshDataList"]);

  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: "",
    time: "",
    area: "",
    occupy: "",
    equivalent: "",
    distribution: "",
  });

  const rules = ref({
    time: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
    area: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
    occupy: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
    equivalent: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
    distribution: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/waterquality/datastatistics/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/waterquality/datastatistics", dataForm).then((res) => {
        ElMessage.success({
          message: $t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
