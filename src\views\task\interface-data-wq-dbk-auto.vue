<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="state.dataForm.monitorTime" type="date" value-format="YYYY-MM-DD" placeholder="监测日期"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 固定表头并将监测时间列固定 -->
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="180" prop="monitorTime" :label="$t('dbk.monitorTime')" header-align="center" align="center" fixed="left"></el-table-column>
      <el-table-column width="150" prop="ph" :label="$t('phytoplankto.ph')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalPhosphorus" :label="$t('dbk.totalPhosphorus')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ammoniaNitrogen" :label="$t('dbk.ammoniaNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="permanganateIndex" :label="$t('dbk.permanganateIndex')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="waterTemperature" :label="$t('dbk.waterTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dissolvedOxygen" :label="$t('dbk.dissolvedOxygen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="conductivity" :label="$t('dbk.conductivity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chlorophyll" :label="$t('dbk.chlorophyll')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="turbidity" :label="$t('dbk.turbidity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalNitrogen" :label="$t('dbk.totalNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="phosphate" :label="$t('dbk.phosphate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nitrate" :label="$t('dbk.nitrate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chemicalOxygenDemand" :label="$t('dbk.chemicalOxygenDemand')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onMounted, reactive, ref, toRefs, onBeforeUnmount } from "vue";
// 兼容组件没有 default export
import AddOrUpdate from "./interface-data-wq-dbk-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
//以下注释不能删除，否则会报错
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-ignore
import { SM4Util } from "sm4util/sm4";
import axios from "axios";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/interface_data_wq_dbk/page",
  getDataListIsPage: true,
  exportURL: "/task/interface_data_wq_dbk/export",
  deleteURL: "/task/interface_data_wq_dbk",
  uploadURL: "/task/interface_data_wq_dbk/upload",
  ExcelURL: "/task/interface_data_wq_dbk/excel",
  dataForm: {
    id: "",
    monitorTime: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const tableHeight = ref<number>(400);
const calculateTableHeight = () => {
  const offset = 220; // 根据页面顶部控件与分页高度调整
  const h = window.innerHeight - offset;
  tableHeight.value = h > 200 ? h : 200;
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener('resize', calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateTableHeight);
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};
</script>

