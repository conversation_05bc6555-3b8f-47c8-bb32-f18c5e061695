<script setup>
import { ref } from "vue";
const props = defineProps({
  url: {
    type: String,
    required: true
  },
  urlRes: {
    required: true
  }
});
</script>

<template>
  <div class="container-iamge">
    <el-image :preview-src-list="[props.url, props.urlRes]" lazy style="width: 700px; height: 650px" :src="props.url" fit="cover"> </el-image>
    <el-image :preview-src-list="[props.urlRes, props.url]" lazy :src="props.urlRes" fit="cover" style="width: 700px; height: 650px" />
  </div>
</template>

<style scoped>
.container-iamge {
  display: flex;
  justify-content: space-around;
}
</style>
