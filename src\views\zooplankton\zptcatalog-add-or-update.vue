<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('zooplankton.zptCategoryName')" prop="zptCategoryName">
        <el-input v-model="dataForm.zptCategoryName" :placeholder="$t('zooplankton.zptCategoryName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptCategoryLatinName')" prop="zptCategoryLatinName">
        <el-input v-model="dataForm.zptCategoryLatinName" :placeholder="$t('zooplankton.zptCategoryLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptGenusName')" prop="zptGenusName">
        <el-input v-model="dataForm.zptGenusName" :placeholder="$t('zooplankton.zptGenusName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptGenusLatinName')" prop="zptGenusLatinName">
        <el-input v-model="dataForm.zptGenusLatinName" :placeholder="$t('zooplankton.zptGenusLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptSpeciesName')" prop="zptSpeciesName">
        <el-input v-model="dataForm.zptSpeciesName" :placeholder="$t('zooplankton.zptSpeciesName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptSpeciesLatinName')" prop="zptSpeciesLatinName">
        <el-input v-model="dataForm.zptSpeciesLatinName" :placeholder="$t('zooplankton.zptSpeciesLatinName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('zooplankton.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.updateAt')" prop="updateAt">
        <el-input v-model="dataForm.updateAt" :placeholder="$t('zooplankton.updateAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.deleteAt')" prop="deleteAt">
        <el-input v-model="dataForm.deleteAt" :placeholder="$t('zooplankton.deleteAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('zooplankton.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  zptCategoryName: "",
  zptCategoryLatinName: "",
  zptGenusName: "",
  zptGenusLatinName: "",
  zptSpeciesName: "",
  zptSpeciesLatinName: "",
  createdAt: "",
  updateAt: "",
  deleteAt: "",
  remark: ""
});

const rules = ref({
  zptCategoryName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptCategoryLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptGenusName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptGenusLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptSpeciesName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptSpeciesLatinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  updateAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  deleteAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/zooplankton/zptcatalog/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/zooplankton/zptcatalog", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
