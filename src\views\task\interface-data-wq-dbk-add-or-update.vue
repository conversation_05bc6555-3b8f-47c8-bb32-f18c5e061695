<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('dbk.monitorTime')" prop="monitorTime">
        <el-input v-model="dataForm.monitorTime" :placeholder="$t('dbk.monitorTime')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.ph')" prop="ph">
        <el-input v-model="dataForm.ph" :placeholder="$t('phytoplankto.ph')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.totalPhosphorus')" prop="totalPhosphorus">
        <el-input v-model="dataForm.totalPhosphorus" :placeholder="$t('phytoplankto.totalPhosphorus')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.ammoniaNitrogen')" prop="ammoniaNitrogen">
        <el-input v-model="dataForm.ammoniaNitrogen" :placeholder="$t('phytoplankto.ammoniaNitrogen')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.permanganateIndex')" prop="permanganateIndex">
        <el-input v-model="dataForm.permanganateIndex" :placeholder="$t('phytoplankto.permanganateIndex')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.waterTemperature')" prop="waterTemperature">
        <el-input v-model="dataForm.waterTemperature" :placeholder="$t('phytoplankto.waterTemperature')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.dissolvedOxygen')" prop="dissolvedOxygen">
        <el-input v-model="dataForm.dissolvedOxygen" :placeholder="$t('zhdc.dissolvedOxygen')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  monitorTime: "",
  ph: "",
  totalPhosphorus: "",
  ammoniaNitrogen: "",
  permanganateIndex: "",
  waterTemperature: "",
  dissolvedOxygen: "",
});

const rules = ref({
  monitorTime: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  ph: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  totalPhosphorus: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  ammoniaNitrogen: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  permanganateIndex: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  waterTemperature: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  dissolvedOxygen: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/task/interface_data_wq_dbk/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/task/interface_data_wq_dbk", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
