<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('bga.address')" prop="address">
        <el-input v-model="dataForm.address" :placeholder="$t('bga.address')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.latitude')" prop="latitude">
        <el-input v-model="dataForm.latitude" :placeholder="$t('bga.latitude')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.longitude')" prop="longitude">
        <el-input v-model="dataForm.longitude" :placeholder="$t('bga.longitude')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.remark')" prop="memo">
        <el-input v-model="dataForm.memo" :placeholder="$t('bga.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  address: "",
  latitude: "",
  longitude: "",
  memo: "",
  creator: "",
  createDate: "",
  updateDate: ""
});

const rules = ref({
  address: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  latitude: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  longitude: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/bga/taddress/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/bga/taddress", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
