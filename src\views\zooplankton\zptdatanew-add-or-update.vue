<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('zooplankton.samplingId')" prop="sampleId">
        <el-input v-model="dataForm.sampleId" :placeholder="$t('zooplankton.samplingId')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.waterbodyName')" prop="waterbodyName">
        <el-input v-model="dataForm.waterbodyName" :placeholder="$t('zooplankton.waterbodyName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.monitorName')" prop="monitorName">
        <el-input v-model="dataForm.monitorName" :placeholder="$t('zooplankton.monitorName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.monitorUnit')" prop="monitorUnit">
        <el-input v-model="dataForm.monitorUnit" :placeholder="$t('zooplankton.monitorUnit')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.timeYear')" prop="timeYear">
        <el-input v-model="dataForm.timeYear" :placeholder="$t('zooplankton.timeYear')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.timeMonth')" prop="timeMonth">
        <el-input v-model="dataForm.timeMonth" :placeholder="$t('zooplankton.timeMonth')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.timeDay')" prop="timeDay">
        <el-input v-model="dataForm.timeDay" :placeholder="$t('zooplankton.timeDay')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.timeDetail')" prop="timeDetail">
        <el-input v-model="dataForm.timeDetail" :placeholder="$t('zooplankton.timeDetail')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.samplingVolume')" prop="samplingVolume">
        <el-input v-model="dataForm.samplingVolume" :placeholder="$t('zooplankton.samplingVolume')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptPhylum')" prop="zptPhylum">
        <el-input v-model="dataForm.zptPhylum" :placeholder="$t('zooplankton.zptPhylum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptClass')" prop="zptClass">
        <el-input v-model="dataForm.zptClass" :placeholder="$t('zooplankton.zptClass')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptOrder')" prop="zptOrder">
        <el-input v-model="dataForm.zptOrder" :placeholder="$t('zooplankton.zptOrder')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptFamily')" prop="zptFamily">
        <el-input v-model="dataForm.zptFamily" :placeholder="$t('zooplankton.zptFamily')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptGenus')" prop="zptGenus">
        <el-input v-model="dataForm.zptGenus" :placeholder="$t('zooplankton.zptGenus')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptName')" prop="zptName">
        <el-input v-model="dataForm.zptName" :placeholder="$t('zooplankton.zptName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.zptLatin')" prop="zptLatin">
        <el-input v-model="dataForm.zptLatin" :placeholder="$t('zooplankton.zptLatin')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.density')" prop="density">
        <el-input v-model="dataForm.density" :placeholder="$t('zooplankton.density')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.wetWeight')" prop="wetWeight">
        <el-input v-model="dataForm.wetWeight" :placeholder="$t('zooplankton.wetWeight')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.biomass')" prop="biomass">
        <el-input v-model="dataForm.biomass" :placeholder="$t('zooplankton.biomass')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zooplankton.notes')" prop="notes">
        <el-input v-model="dataForm.notes" :placeholder="$t('zooplankton.notes')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  sampleId: "",
  waterbodyName: "",
  monitorName: "",
  monitorUnit: "",
  timeYear: "",
  timeMonth: "",
  timeDay: "",
  timeDetail: "",
  samplingVolume: "",
  zptPhylum: "",
  zptClass: "",
  zptOrder: "",
  zptFamily: "",
  zptGenus: "",
  zptName: "",
  zptLatin: "",
  density: "",
  wetWeight: "",
  biomass: "",
  notes: ""
});

const rules = ref({
  sampleId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterbodyName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  monitorName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  monitorUnit: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeYear: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeMonth: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDay: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDetail: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  samplingVolume: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptPhylum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptClass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptOrder: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptFamily: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptGenus: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zptLatin: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  density: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  wetWeight: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  biomass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  notes: [{ required: false, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/zooplankton/zptdatanew/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/zooplankton/zptdatanew", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
