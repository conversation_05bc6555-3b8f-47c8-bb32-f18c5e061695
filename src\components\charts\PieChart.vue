<template>
  <div ref="chartRef" style="width: 1000px; height: 400px;"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  chartData: {
    type: Array,
    required: true,
    default: () => []
  },
  title: {
    type: String,
    default: '数据占比'
  }
});

const chartRef = ref(null);
let chartInstance = null;

const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);

  const option = {
    title: {
      text: props.title,
      left: "center"
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      data: props.chartData.map(item => item.name)
    },
    series: [
      {
        name: "数据占比",
        type: "pie",
        //radius: ["50%", "70%"],
        radius: [20, 120],
        roseType: 'area',
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: "{b}: {c} ({d}%)"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "24",
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: true
        },
        data: props.chartData
      }
    ]
  };

  chartInstance.setOption(option);
};

const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption({
      legend: {
        data: props.chartData.map((item) => item.name)
      },
      series: [
        {
          data: props.chartData
        }
      ]
    });
  }
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", () => {
    chartInstance?.resize();
  });
});

watch(() => props.chartData, () => {
  updateChart();
}, { deep: true });
</script>
