<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('waterquality.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('waterquality.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.verticalLineNum')" prop="verticalLineNum">
        <el-input v-model="dataForm.verticalLineNum" :placeholder="$t('waterquality.verticalLineNum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.layerNum')" prop="layerNum">
        <el-input v-model="dataForm.layerNum" :placeholder="$t('waterquality.layerNum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.waterTemperature')" prop="waterTemperature">
        <el-input v-model="dataForm.waterTemperature" :placeholder="$t('waterquality.waterTemperature')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.ph')" prop="ph">
        <el-input v-model="dataForm.ph" :placeholder="$t('waterquality.ph')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.electricalConductivity')" prop="electricalConductivity">
        <el-input v-model="dataForm.electricalConductivity" :placeholder="$t('waterquality.electricalConductivity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.turbidity')" prop="turbidity">
        <el-input v-model="dataForm.turbidity" :placeholder="$t('waterquality.turbidity')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.dissolvedOxygen')" prop="dissolvedOxygen">
        <el-input v-model="dataForm.dissolvedOxygen" :placeholder="$t('waterquality.dissolvedOxygen')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.transparency')" prop="transparency">
        <el-input v-model="dataForm.transparency" :placeholder="$t('waterquality.transparency')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.orp')" prop="orp">
        <el-input v-model="dataForm.orp" :placeholder="$t('waterquality.orp')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.underwaterIllumination')" prop="underwaterIllumination">
        <el-input v-model="dataForm.underwaterIllumination" :placeholder="$t('waterquality.underwaterIllumination')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.suspendedSolidsConcentration')" prop="suspendedSolidsConcentration">
        <el-input v-model="dataForm.suspendedSolidsConcentration" :placeholder="$t('waterquality.suspendedSolidsConcentration')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.createdAt')" prop="createdAt">
        <el-input v-model="dataForm.createdAt" :placeholder="$t('waterquality.createdAt')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('waterquality.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('waterquality.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationCode: "",
  verticalLineNum: "",
  layerNum: "",
  createdAt: "",
  waterTemperature: "",
  ph: "",
  electricalConductivity: "",
  turbidity: "",
  dissolvedOxygen: "",
  transparency: "",
  orp: "",
  underwaterIllumination: "",
  suspendedSolidsConcentration: "",
  remark: ""
});

const rules = ref({
  stationCode: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  verticalLineNum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  layerNum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createdAt: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterTemperature: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  ph: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  electricalConductivity: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  turbidity: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  dissolvedOxygen: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  transparency: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  orp: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  underwaterIllumination: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  suspendedSolidsConcentration: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  remark: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/waterquality/wqphysicalinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/waterquality/wqphysicalinfo", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
