<template>
  <div class="grid">
    <home-card></home-card>
  </div>
  <div class="chart-row">
    <div class="chart-container">
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabChange" :before-leave="handleBeforeTabLeave">
        <el-tab-pane label="叶绿素a" name="chlorophyllA">
          <!-- 移除多余的 chart-wrapper -->
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in lakeStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab === 'chlorophyllA'" ref="chlorophyllChart" :chart-data="chlorophyllData" title="湖体近一周叶绿素a变化趋势" unit="μg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="总氮" name="totalNitrogen">
          <!-- 移除多余的 chart-wrapper -->
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in lakeStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab === 'totalNitrogen'" ref="nitrogenChart" :chart-data="nitrogenData" title="湖体近一周总氮变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="总磷" name="totalPhosphorus">
          <!-- 移除多余的 chart-wrapper -->
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in lakeStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab === 'totalPhosphorus'" ref="phosphorusChart" :chart-data="phosphorusData" title="湖体近一周总磷变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="氨氮" name="ammoniaNitrogen">
          <!-- 移除多余的 chart-wrapper -->
          <!-- 添加站点选择器 -->
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in lakeStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab === 'ammoniaNitrogen'" ref="ammoniaChart" :chart-data="ammoniaData" title="湖体近一周氨氮变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="chart-container">
      <el-tabs v-model="activeTab_hd" type="card" @tab-click="handleTabChange_hd" :before-leave="handleBeforeTabLeave">
        <el-tab-pane label="溶解氧" name="dissolvedOxygen">
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedRiverStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in RiverStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab_hd === 'dissolvedOxygen'" ref="dissolvedOxygenChart"
                      :chart-data="dissolvedOxygenData" title="河道近一周溶解氧变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="氨氮" name="ammoniaNitrogen">
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedRiverStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in RiverStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab_hd === 'ammoniaNitrogen'" ref="ammoniaNitrogenChart"
                      :chart-data="ammoniaNitrogenData" title="河道近一周氨氮变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="总磷" name="totalPhosphorus">
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedRiverStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in RiverStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab_hd === 'totalPhosphorus'" ref="totalPhosphorusChart"
                      :chart-data="totalPhosphorusData" title="河道近一周总磷变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="总氮" name="totalNitrogen">
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedRiverStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in RiverStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab_hd === 'totalNitrogen'" ref="totalNitrogenChart"
                      :chart-data="totalNitrogenData" title="河道近一周总氮变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
        <el-tab-pane label="高锰酸盐指数" name="permanganateIndex">
          <div style="margin-bottom: 15px;">
            <el-select
              v-model="selectedRiverStation"
              placeholder="请选择站点"
              clearable
              @change="handleStationChange"
              style="width: 200px"
            >
              <el-option
                v-for="station in RiverStationOptions"
                :key="station.stationCode"
                :label="station.stationName"
                :value="station.stationCode"
              />
            </el-select>
          </div>
          <line-chart v-if="activeTab_hd === 'permanganateIndex'" ref="permanganateIndexChart"
                      :chart-data="permanganateIndexData" title="河道近一周高锰酸盐指数变化趋势" unit="mg/L"></line-chart>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, onUnmounted } from "vue";
import HomeCard from "@/components/home-card/HomeCard.vue";
//import HomePie from "@/components/home-pie/HomePie.vue";
import LineChart from "@/components/charts/LineChart.vue";
//import PieChart from "@/components/charts/PieChart.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

// 活动标签页
const activeTab = ref("chlorophyllA");
const activeTab_hd = ref("dissolvedOxygen");

// 筛选表单
const filterForm = reactive({
  stationCode: "",
  stationName: "",
  dateRange: [getDefaultStartDate(), getDefaultEndDate()]
});

// 站点变化处理函数
function handleStationChange() {
  fetchChartData();
}

// 站点选项
const stationOptions = ref<Array<{ stationCode: string; stationName: string }>>([]);
const stationOptions_hd = ref<Array<{ stationCode: string; stationName: string }>>([]);

// 湖体图表数据
const chlorophyllData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const nitrogenData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const phosphorusData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const ammoniaData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
// 河道图表数据
const dissolvedOxygenData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const ammoniaNitrogenData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const totalPhosphorusData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const totalNitrogenData = reactive({
  dates: [] as string[],
  values: [] as number[]
});
const permanganateIndexData = reactive({
  dates: [] as string[],
  values: [] as number[]
});

// 湖体图表引用
const chlorophyllChart = ref();
const nitrogenChart = ref();
const phosphorusChart = ref();
const ammoniaChart = ref();
// 河道图表引用
const dissolvedOxygenChart = ref();
const ammoniaNitrogenChart = ref();
const totalPhosphorusChart = ref();
const totalNitrogenChart = ref();
const permanganateIndexChart = ref();

const selectedStation = ref("");
const lakeStationOptions = ref([
  { stationCode: "541", stationName: "观音山中" },
  { stationCode: "543", stationName: "观音山西" },
  { stationCode: "545", stationName: "滇池南" },
  { stationCode: "539", stationName: "灰湾中" },
  { stationCode: "544", stationName: "海口西" },
  { stationCode: "546", stationName: "白鱼口" },
  { stationCode: "540", stationName: "罗家营" },
  { stationCode: "541", stationName: "观音山东" },
  { stationCode: "547", stationName: "断桥" },
  { stationCode: "538", stationName: "草海中心" }
]);
const selectedRiverStation = ref("");
const RiverStationOptions = ref([
  { stationCode: "525", stationName: "回龙村" },
  { stationCode: "526", stationName: "船房五社桥头(一检站)" },
  { stationCode: "527", stationName: "王大桥" },
  { stationCode: "528", stationName: "大观河入湖口(航运公司)(篆塘河泵站)" },
  { stationCode: "529", stationName: "大渔乡土罗村入湖口" },
  { stationCode: "530", stationName: "新河村入湖口(金属筛片厂小桥)" },
  { stationCode: "533", stationName: "严家村桥" },
  { stationCode: "534", stationName: "宝丰村入湖口" },
  { stationCode: "535", stationName: "江尾下闸" },
  { stationCode: "536", stationName: "茨巷河入湖口(牛恋乡)" },
  { stationCode: "537", stationName: "东大河滇池入湖口" },
  { stationCode: "548", stationName: "晋城小寨" }
]);

// 获取默认开始日期（7天前）
function getDefaultStartDate() {
  const date = new Date();
  date.setDate(date.getDate() - 7);
  return formatDate(date);
}

// 获取默认结束日期（今天）
function getDefaultEndDate() {
  return formatDate(new Date());
}

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

// 重置筛选条件
function resetFilters() {
  filterForm.stationCode = "";
  filterForm.stationName = "";
  filterForm.dateRange = [getDefaultStartDate(), getDefaultEndDate()];
  fetchChartData();
}

// 处理标签切换
// 修改handleTabChange函数
function handleTabChange(tab: { paneName: string }) {
  nextTick(() => {
    // 等待DOM更新完成
    setTimeout(() => {
      let currentChartRef;
      switch (tab.paneName) {
        case "chlorophyllA":
          currentChartRef = chlorophyllChart.value;
          break;
        case "totalNitrogen":
          currentChartRef = nitrogenChart.value;
          break;
        case "totalPhosphorus":
          currentChartRef = phosphorusChart.value;
          break;
        case "ammoniaNitrogen":
          currentChartRef = ammoniaChart.value;
          break;
      }

      if (currentChartRef) {
        currentChartRef.resize();
      }
    });
  });
}
// 河道数据标签切换
function handleTabChange_hd(tab: { paneName: string }) {
  nextTick(() => {
    setTimeout(() => {
      let currentChartRef;
      switch (tab.paneName) {
        case "dissolvedOxygen":
          currentChartRef = dissolvedOxygenChart.value;
          break;
        case "ammoniaNitrogen":
          currentChartRef = ammoniaNitrogenChart.value;
          break;
        case "totalPhosphorus":
          currentChartRef = totalPhosphorusChart.value;
          break;
        case "totalNitrogen":
          currentChartRef = totalNitrogenChart.value;
          break;
        case "permanganateIndex":
          currentChartRef = permanganateIndexChart.value;
          break;
      }

      if (currentChartRef) {
        currentChartRef.resize();
      }
    });
  });
}

// 获取站点列表
async function fetchStationOptions() {
  try {
    const response = await baseService.get("/bga/interface_data_huti/page", {
      params: {
        page: 1,
        limit: 500, // 获取足够多的数据以确保包含所有站点
        orderField: "station_code",
        order: "asc"
      }
    });
    const response_hd = await baseService.get("/task/interface_data_hedao_guokong/page", {
      params:{
        page: 1,
        limit: 500, // 获取足够多的数据以确保包含所有站点
        orderField: "station_code",
        order: "asc"
      }
    });

    // 去重处理
    const uniqueStations: { stationCode: string; stationName: string }[] = [];
    const uniqueStations_hd: { stationCode: string; stationName: string }[] = [];
    const seen = new Set();
    const seen_hd = new Set();

    response.data.list.forEach((item: any) => {
      if (!seen.has(item.stationCode)) {
        seen.add(item.stationCode);
        uniqueStations.push({
          stationCode: item.stationCode,
          stationName: item.stationName || `站点${item.stationCode}`
        });
      }
    });

    response_hd.data.list.forEach((item: any) => {
      if (!seen_hd.has(item.stationCode)) {
        seen_hd.add(item.stationCode);
        uniqueStations_hd.push({
          stationCode: item.stationCode,
          stationName: item.stationName || `站点${item.stationCode}`
        });
      }
    });

    stationOptions.value = uniqueStations;
    stationOptions_hd.value = uniqueStations_hd;
  } catch (error) {
    console.error("获取站点列表失败:", error);
    ElMessage.error("获取站点列表失败");
  }
}

// 获取图表数据
async function fetchChartData() {
  const params: Record<string, any> = {
    page: 1,
    limit: 1000, // 获取足够多的数据
    orderField: "monitor_time",
    order: "asc",
    // 明确传递开始和结束日期参数
    startTime: `${filterForm.dateRange[0]} 00:00:00`,
    endTime: `${filterForm.dateRange[1]} 23:59:59`
  };

  // 如果选择了站点，添加站点筛选条件
  if (selectedStation.value) {
    params.stationCode = selectedStation.value;
  }
  if (selectedRiverStation.value) {
    params.stationCode = selectedRiverStation.value;
  }

  // 添加筛选条件
  if (filterForm.stationCode) {
    params.stationCode = filterForm.stationCode;
    params.stationName = filterForm.stationName;
  }

  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    params.startTime = `${filterForm.dateRange[0]} 00:00:00`;
    params.endTime = `${filterForm.dateRange[1]} 23:59:59`;
  }

  try {
    const response = await baseService.get("/bga/interface_data_huti/interface/getHutiAutoDataApi", { params });
    const response_hd = await baseService.get("/task/interface_data_hedao_guokong/interface/getHedaoAutoDataApi", { params });
    if (response.data.list.length === 0) {
      ElMessage.warning("没有找到符合条件的数据");
      return;
    }

    // 处理数据 - 确保包含7天的数据
    processChartDataWith7Days(response.data.list);
    processHedaoChartDataWith7Days(response_hd.data.list);

    // 更新图表
    updateCharts();
  } catch (error) {
    console.error("获取图表数据失败:", error);
    ElMessage.error("获取图表数据失败");
  }
}
// 计算平均值
function calculateAverage(values: number[]): number {
  if (!values || values.length === 0) return 0;
  const validValues = values.filter((v) => !isNaN(v));
  if (validValues.length === 0) return 0;
  const sum = validValues.reduce((a, b) => a + b, 0);
  return parseFloat((sum / validValues.length).toFixed(5));
}

// 更新图表
function updateCharts() {
  chlorophyllChart.value?.updateChart();
  nitrogenChart.value?.updateChart();
  phosphorusChart.value?.updateChart();
  ammoniaChart.value?.updateChart();

  dissolvedOxygenChart.value?.updateChart();
  ammoniaNitrogenChart.value?.updateChart();
  totalPhosphorusChart.value?.updateChart();
  totalNitrogenChart.value?.updateChart();
  permanganateIndexChart.value?.updateChart();
}
// 添加窗口resize处理函数
function handleWindowResize() {
  nextTick(() => {
    const charts = [chlorophyllChart.value, nitrogenChart.value, phosphorusChart.value, ammoniaChart.value];

    charts.forEach((chart) => {
      if (chart) {
        chart.resize();
      }
    });
  });
}
// 组件挂载时获取数据
onMounted(() => {
  fetchStationOptions();
  fetchChartData();
  window.addEventListener('resize', handleWindowResize);
});
onUnmounted(() => {
  // 移除监听
  window.removeEventListener('resize', handleWindowResize);
});

// 处理图表数据 - 确保包含7天的数据
function processChartDataWith7Days(data: any[]) {
  // 清空数据
  chlorophyllData.dates = [];
  chlorophyllData.values = [];
  nitrogenData.dates = [];
  nitrogenData.values = [];
  phosphorusData.dates = [];
  phosphorusData.values = [];
  ammoniaData.dates = [];
  ammoniaData.values = [];

  // 1. 确定日期范围（最近7天）
  const endDate = new Date(); // 今天
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 6); // 7天范围（包含今天）

  // 2. 生成7天的日期数组
  const dateRange: string[] = [];
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    dateRange.push(formatDate(new Date(d)));
  }

  // 3. 按日期分组现有数据
  const groupedData: Record<
    string,
    {
      date: string;
      chlorophyllA: number[];
      totalNitrogen: number[];
      totalPhosphorus: number[];
      ammoniaNitrogen: number[];
    }
  > = {};

  // 初始化所有日期的数据结构
  dateRange.forEach((date) => {
    groupedData[date] = {
      date,
      chlorophyllA: [],
      totalNitrogen: [],
      totalPhosphorus: [],
      ammoniaNitrogen: []
    };
  });

  // 过滤数据 - 如果选择了站点，只处理该站点的数据
  const filteredData = selectedStation.value ? data.filter((item) => item.stationCode === Number(selectedStation.value)) : data;

  // 填充实际数据
  filteredData.forEach((item: any) => {
    if (!item.monitorTime) return;

    // 标准化日期格式
    const dateObj = new Date(item.monitorTime);
    const dateKey = formatDate(dateObj);

    if (groupedData[dateKey]) {
      if (item.chlorophyllA !== undefined && item.chlorophyllA !== null) {
        groupedData[dateKey].chlorophyllA.push(Number(item.chlorophyllA));
      }
      if (item.totalNitrogen !== undefined && item.totalNitrogen !== null) {
        groupedData[dateKey].totalNitrogen.push(Number(item.totalNitrogen));
      }
      if (item.totalPhosphorus !== undefined && item.totalPhosphorus !== null) {
        groupedData[dateKey].totalPhosphorus.push(Number(item.totalPhosphorus));
      }
      if (item.ammoniaNitrogen !== undefined && item.ammoniaNitrogen !== null) {
        groupedData[dateKey].ammoniaNitrogen.push(Number(item.ammoniaNitrogen));
      }
    }
  });

  // 4. 填充图表数据 - 确保显示7天
  dateRange.forEach((date) => {
    const dayData = groupedData[date];

    // 添加日期
    chlorophyllData.dates.push(dayData.date);
    nitrogenData.dates.push(dayData.date);
    phosphorusData.dates.push(dayData.date);
    ammoniaData.dates.push(dayData.date);

    // 计算平均值，没有数据则显示null（图表中会显示断点）
    chlorophyllData.values.push(calculateAverage(dayData.chlorophyllA));
    nitrogenData.values.push(calculateAverage(dayData.totalNitrogen));
    phosphorusData.values.push(calculateAverage(dayData.totalPhosphorus));
    ammoniaData.values.push(calculateAverage(dayData.ammoniaNitrogen));
  });

  console.log("生成的7天图表数据:", {
    dates: chlorophyllData.dates,
    chlorophyll: chlorophyllData.values,
    nitrogen: nitrogenData.values,
    phosphorus: phosphorusData.values,
    ammonia: ammoniaData.values
  });
}
const handleBeforeTabLeave = (newTab: string, oldTab: string) => {
  // 保存当前滚动位置
  const scrollY = window.scrollY;

  // 在nextTick后恢复滚动位置
  nextTick(() => {
    setTimeout(() => {
      window.scrollTo(0, scrollY);
    }, 0);
  });

  return true; // 允许切换
};

// 处理河道图表数据 - 确保包含7天的数据
function processHedaoChartDataWith7Days(data: any[]) {
  // 清空数据
  dissolvedOxygenData.dates = [];
  dissolvedOxygenData.values = [];
  ammoniaNitrogenData.dates = [];
  ammoniaNitrogenData.values = [];
  totalPhosphorusData.dates = [];
  totalPhosphorusData.values = [];
  totalNitrogenData.dates = [];
  totalNitrogenData.values = [];
  permanganateIndexData.dates = [];
  permanganateIndexData.values = [];

  // 1. 确定日期范围（最近7天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 6);

  // 2. 生成7天的日期数组
  const dateRange: string[] = [];
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    dateRange.push(formatDate(new Date(d)));
  }

  // 3. 按日期分组现有数据
  const groupedData: Record<
    string,
    {
      date: string;
      dissolvedOxygen: number[];
      ammoniaNitrogen: number[];
      totalPhosphorus: number[];
      totalNitrogen: number[];
      permanganateIndex: number[];
    }
  > = {};

  // 初始化所有日期的数据结构
  dateRange.forEach((date) => {
    groupedData[date] = {
      date,
      dissolvedOxygen: [],
      ammoniaNitrogen: [],
      totalPhosphorus: [],
      totalNitrogen: [],
      permanganateIndex: []
    };
  });

  const filteredData = selectedRiverStation.value ? data.filter((item) => item.stationCode === selectedRiverStation.value) : data;

  // 填充实际数据
  filteredData.forEach((item: any) => {
    if (!item.monitorTime) return;

    const dateObj = new Date(item.monitorTime);
    const dateKey = formatDate(dateObj);

    if (groupedData[dateKey]) {
      if (item.dissolvedOxygen !== undefined && item.dissolvedOxygen !== null) {
        groupedData[dateKey].dissolvedOxygen.push(Number(item.dissolvedOxygen));
      }
      if (item.ammoniaNitrogen !== undefined && item.ammoniaNitrogen !== null) {
        groupedData[dateKey].ammoniaNitrogen.push(Number(item.ammoniaNitrogen));
      }
      if (item.totalPhosphorus !== undefined && item.totalPhosphorus !== null) {
        groupedData[dateKey].totalPhosphorus.push(Number(item.totalPhosphorus));
      }
      if (item.totalNitrogen !== undefined && item.totalNitrogen !== null) {
        groupedData[dateKey].totalNitrogen.push(Number(item.totalNitrogen));
      }
      if (item.permanganateIndex !== undefined && item.permanganateIndex !== null) {
        groupedData[dateKey].permanganateIndex.push(Number(item.permanganateIndex));
      }
    }
  });

  // 4. 填充图表数据 - 确保显示7天
  dateRange.forEach((date) => {
    const dayData = groupedData[date];

    // 添加日期
    dissolvedOxygenData.dates.push(dayData.date);
    ammoniaNitrogenData.dates.push(dayData.date);
    totalPhosphorusData.dates.push(dayData.date);
    totalNitrogenData.dates.push(dayData.date);
    permanganateIndexData.dates.push(dayData.date);

    // 计算平均值
    dissolvedOxygenData.values.push(calculateAverage(dayData.dissolvedOxygen));
    ammoniaNitrogenData.values.push(calculateAverage(dayData.ammoniaNitrogen));
    totalPhosphorusData.values.push(calculateAverage(dayData.totalPhosphorus));
    totalNitrogenData.values.push(calculateAverage(dayData.totalNitrogen));
    permanganateIndexData.values.push(calculateAverage(dayData.permanganateIndex));
  });
}
</script>

<style scoped lang="less">
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 2fr));
  gap: 20px;
  padding: 20px;
}

.chart-row {
  display: flex; // 使用flex布局
  gap: 30px; // 设置两个图表之间的间距
  width: 100%; // 确保占据整个宽度
  margin-bottom: 20px; // 与下方内容的间距
}

.chart-container {
  grid-column: span 2;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 50%;
}

.chart-wrapper {
  width: 100%;
  height: 200px;
  min-height: 200px;
  position: relative; /* 确保图表正确填充容器 */
  /* 确保图表元素填充容器 */
  & > div {
    width: 100% !important;
    height: 100% !important;
  }
}

.filter-container {
  margin-top: 20px;

  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
