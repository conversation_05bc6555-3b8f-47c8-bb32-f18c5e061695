<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="水体名称" prop="waterName">
        <el-input v-model="dataForm.waterName" placeholder="水体名称"></el-input>
      </el-form-item>
      <el-form-item label="监测站点名称" prop="stationName">
        <el-input v-model="dataForm.stationName" placeholder="监测站点名称"></el-input>
      </el-form-item>
      <el-form-item label="监测单位" prop="monitoringUnits">
        <el-input v-model="dataForm.monitoringUnits" placeholder="监测单位"></el-input>
      </el-form-item>
      <el-form-item label="年" prop="year">
        <el-input v-model="dataForm.year" placeholder="年"></el-input>
      </el-form-item>
      <el-form-item label="月" prop="month">
        <el-input v-model="dataForm.month" placeholder="月"></el-input>
      </el-form-item>
      <el-form-item label="日" prop="day">
        <el-input v-model="dataForm.day" placeholder="日"></el-input>
      </el-form-item>
      <el-form-item label="具体时间" prop="time">
        <el-input v-model="dataForm.time" placeholder="具体时间"></el-input>
      </el-form-item>
      <el-form-item label="水温" prop="waterTemperature">
        <el-input v-model="dataForm.waterTemperature" placeholder="水温"></el-input>
      </el-form-item>
      <el-form-item label="pH无量纲" prop="ph">
        <el-input v-model="dataForm.ph" placeholder="pH无量纲"></el-input>
      </el-form-item>
      <el-form-item label="溶解氧mg/L" prop="dissolvedOxygen">
        <el-input v-model="dataForm.dissolvedOxygen" placeholder="溶解氧mg/L"></el-input>
      </el-form-item>
      <el-form-item label="高锰酸盐指数 (mg/L)" prop="permanganateIndex">
        <el-input v-model="dataForm.permanganateIndex" placeholder="高锰酸盐指数 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="化学需氧量 (mg/L)" prop="chemicalOxygenDemand">
        <el-input v-model="dataForm.chemicalOxygenDemand" placeholder="化学需氧量 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="五日生化需氧量(mg/L)" prop="bod5">
        <el-input v-model="dataForm.bod5" placeholder="五日生化需氧量(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="氨氮(mg/L)" prop="ammoniaNitrogen">
        <el-input v-model="dataForm.ammoniaNitrogen" placeholder="氨氮(mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="总磷 (mg/L)" prop="totalPhosphorus">
        <el-input v-model="dataForm.totalPhosphorus" placeholder="总磷 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="总氮 (mg/L)" prop="totalNitrogen">
        <el-input v-model="dataForm.totalNitrogen" placeholder="总氮 (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="透明度 (m)" prop="transparency">
        <el-input v-model="dataForm.transparency" placeholder="透明度 (m)"></el-input>
      </el-form-item>
      <el-form-item label="叶绿素a (mg/L)" prop="chlorophyllA">
        <el-input v-model="dataForm.chlorophyllA" placeholder="叶绿素a (mg/L)"></el-input>
      </el-form-item>
      <el-form-item label="电导率 (μs/cm)" prop="electricalConductivity">
        <el-input v-model="dataForm.electricalConductivity" placeholder="电导率 (μs/cm)"></el-input>
      </el-form-item>
      <el-form-item label="浊度 (NTU)" prop="turbidity">
        <el-input v-model="dataForm.turbidity" placeholder="浊度 (NTU)"></el-input>
      </el-form-item>
      <el-form-item label="藻生物量（仪器）万个/L" prop="algalBiomass">
        <el-input v-model="dataForm.algalBiomass" placeholder="藻生物量（仪器）万个/L"></el-input>
      </el-form-item>
      <el-form-item label="水质类别" prop="waterQuality">
        <el-input v-model="dataForm.waterQuality" placeholder="水质类别"></el-input>
      </el-form-item>
      <el-form-item label="wpi指数" prop="wpi">
        <el-input v-model="dataForm.wpi" placeholder="wpi指数"></el-input>
      </el-form-item>
      <el-form-item label="wpi水质类别" prop="wpiQuality">
        <el-input v-model="dataForm.wpiQuality" placeholder="wpi水质类别"></el-input>
      </el-form-item>
      <el-form-item label="综合污染指数" prop="comprehensivePollution">
        <el-input v-model="dataForm.comprehensivePollution" placeholder="综合污染指数"></el-input>
      </el-form-item>
      <el-form-item label="综合营养状态指数" prop="comprehensiveNutritional">
        <el-input v-model="dataForm.comprehensiveNutritional" placeholder="综合营养状态指数"></el-input>
      </el-form-item>
      <el-form-item label="营养状态" prop="nutritionalStatus">
        <el-input v-model="dataForm.nutritionalStatus" placeholder="营养状态"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  const emit = defineEmits(["refreshDataList"]);

  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: '',  waterName: '',  stationName: '',  monitoringUnits: '',  year: '',  month: '',  day: '',  time: '',  waterTemperature: '',  ph: '',  dissolvedOxygen: '',  permanganateIndex: '',  chemicalOxygenDemand: '',  bod5: '',  ammoniaNitrogen: '',  totalPhosphorus: '',  totalNitrogen: '',  transparency: '',  chlorophyllA: '',  electricalConductivity: '',  turbidity: '',  algalBiomass: '',  waterQuality: '',  wpi: '',  wpiQuality: '',  comprehensivePollution: '',  comprehensiveNutritional: '',  nutritionalStatus: ''});

  const rules = ref({
            waterName: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            stationName: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            monitoringUnits: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            year: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            month: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            day: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            time: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            waterTemperature: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            ph: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            dissolvedOxygen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            permanganateIndex: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            chemicalOxygenDemand: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            bod5: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            ammoniaNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            totalPhosphorus: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            totalNitrogen: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            transparency: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            chlorophyllA: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            electricalConductivity: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            turbidity: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            algalBiomass: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            waterQuality: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            wpi: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            wpiQuality: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            comprehensivePollution: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            comprehensiveNutritional: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ],
            nutritionalStatus: [
        { required: true, message: '必填项不能为空', trigger: 'blur' }
      ]
    });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/waterquality/wqgk/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/waterquality/wqgk", dataForm).then((res) => {
        ElMessage.success({
          message: '成功',
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
