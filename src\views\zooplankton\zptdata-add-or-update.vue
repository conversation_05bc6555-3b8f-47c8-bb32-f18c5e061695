<template>
    <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
        <el-form-item :label="$t('phytoplankto.sampleDataId')" prop="samplingId">
          <el-input v-model="dataForm.samplingId" :placeholder="$t('phytoplankto.sampleDataId')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.samplingLocation')" prop="samplingLocation">
          <el-input v-model="dataForm.samplingLocation" :placeholder="$t('phytoplankto.samplingLocation')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.category')" prop="category">
          <el-input v-model="dataForm.category" :placeholder="$t('phytoplankto.category')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.species')" prop="species">
          <el-input v-model="dataForm.species" :placeholder="$t('phytoplankto.species')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.latinName')" prop="latinName">
          <el-input v-model="dataForm.latinName" :placeholder="$t('phytoplankto.latinName')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.wetWeight')" prop="wetWeight">
          <el-input v-model="dataForm.wetWeight" :placeholder="$t('phytoplankto.wetWeight')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.count')" prop="count">
          <el-input v-model="dataForm.count" :placeholder="$t('phytoplankto.count')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.cellCount')" prop="cellCount">
          <el-input v-model="dataForm.cellCount" :placeholder="$t('phytoplankto.cellCount')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('phytoplankto.biomass')" prop="biomass">
          <el-input v-model="dataForm.biomass" :placeholder="$t('phytoplankto.biomass')"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
      </template>
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const emit = defineEmits(["refreshDataList"]);
  
  const visible = ref(false);
  const dataFormRef = ref();
  
  const dataForm = reactive({
    id: "",
    samplingId: "",
    samplingLocation: "",
    category: "",
    species: "",
    latinName: "",
    wetWeight: "",
    count: "",
    cellCount: "",
    biomass: ""
  });
  
  const rules = ref({
    samplingId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    samplingLocation: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    category: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    species: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    latinName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    wetWeight: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    count: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    cellCount: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    biomass: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
  });
  
  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";
  
    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }
  
    if (id) {
      getInfo(id);
    }
  };
  
  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("/zooplankton/zptdata/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };
  
  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/zooplankton/zptdata", dataForm).then((res) => {
        ElMessage.success({
          message: $t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };
  
  defineExpose({
    init
  });
  </script>
  