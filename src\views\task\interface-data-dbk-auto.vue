<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="state.dataForm.monitorTime" type="date" value-format="YYYY-MM-DD" placeholder="监测日期"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" :height="tableHeight" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="monitorTime" :label="$t('dbk.monitorTime')" header-align="center" align="center" fixed="left"></el-table-column>
      <el-table-column width="150" prop="windSpeed" :label="$t('dbk.windSpeed')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="accumulatedRainfall" :label="$t('dbk.accumulatedRainfall')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="atmosphericTemperature" :label="$t('dbk.atmosphericTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="digitalAirPressure" :label="$t('dbk.digitalAirPressure')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="liquidLevel" :label="$t('dbk.liquidLevel')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="evaporation" :label="$t('dbk.evaporation')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="windDirection" :label="$t('dbk.windDirection')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="atmosphericHumidity" :label="$t('dbk.atmosphericHumidity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="tbqTotalRadiation" :label="$t('dbk.tbqTotalRadiation')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="lightIntensity" :label="$t('dbk.lightIntensity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="cumulativeRadiation" :label="$t('dbk.cumulativeRadiation')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onMounted, onBeforeUnmount, reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./interface-data-dbk-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
//以下注释不能删除，否则会报错
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-ignore
import { SM4Util } from "sm4util/sm4";
import axios from "axios";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/interface_data_dbk/page",
  getDataListIsPage: true,
  exportURL: "/task/interface_data_dbk/export",
  deleteURL: "/task/interface_data_dbk",
  uploadURL: "/task/interface_data_dbk/upload",
  ExcelURL: "/task/interface_data_dbk/excel",
  dataForm: {
    id: "",
    monitorTime: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

// 表格高度自动计算（用于固定表头）
const tableHeight = ref<number>(400);

function calculateTableHeight() {
  const offset = 220; // 根据页面顶部控件和分页等高度估算
  tableHeight.value = Math.max(window.innerHeight - offset, 200);
}

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});
</script>

