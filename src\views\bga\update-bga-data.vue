<template>
  <el-dialog v-model="visible" @closed="closeDialogHandle" align-center title="上传数据表" :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="tips"><el-text class="mx-1" type="primary" size="large">请先下载模板</el-text><el-button bg text type="primary" @click="downloadFile">下载模板</el-button></div>
    <el-upload :headers="{ token: getToken() }" :file-list="uploadFiles" ref="uploadRef" class="upload-demo" show-file-list :before-upload="beforeUploadHandle" drag :action="uploadPath" multiple :auto-upload="false">
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">{{ $t("upload.text") }}</div>
    </el-upload>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" ref="ref3" @click="dataFormSubmitHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
import { getToken } from "@/utils/cache";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const uploadFiles = ref([]);
const uploadPath = ref(import.meta.env.VITE_APP_API + "/bga/phytosampledata/upload");
const visible = ref(false);
const dataFormRef = ref();
const num = ref(0);

// 文件上传前
function beforeUploadHandle(rawFile: any) {
  console.log(rawFile);
  num.value++;
}

const init = () => {
  visible.value = true;
  uploadFiles.value = [];

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
};
// 表单提交
const dataFormSubmitHandle = () => {
  uploadRef.value?.submit();
};
const uploadRef = ref();
function closeDialogHandle() {
  uploadFiles.value = [];
  num.value = 0;
}
// 下载模板
const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/template.xlsx"; // 文件相对于 public 文件夹的路径
  downloadLink.download = "数据表模板.xlsx"; // 下载时的文件名

  // 模拟点击下载链接
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};
defineExpose({
  init
});
</script>

<style scoped>
.tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
