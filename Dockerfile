FROM harbor.mybi.top:9180/common/node:20.18.0 as builder

WORKDIR /opt/app

COPY package.json package-lock.json ./


RUN npm install --registry=https://registry.npmmirror.com
#RUN npm install --registry=https://registry.npmjs.org
COPY . .

RUN npm run build


FROM harbor.mybi.top:9180/common/nginx:latest

COPY --from=builder /opt/app/dist /usr/share/nginx/html/

COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
