## renren-ui
- renren-ui是基于Vue3、TypeScript、Element Plus、Vue Router、Pinia、Axios、Vite等开发，实现 【[renren-security](https://gitee.com/renrenio/renren-security)】 后台管理前端功能，提供一套更优的前端解决方案
- 前后端分离，通过token进行数据交互，可独立部署
- 动态菜单，通过菜单管理统一管理访问路由
<br> 

## 安装

您需要提前在本地安装[Node.js](https://nodejs.org/en/)，版本号为：[14.18+、16.x]，再使用[Git](https://git-scm.com/)克隆项目或者直接下载项目后，然后通过`终端命令行`执行以下命令。

```bash

npm install -g cnpm --registry=https://registry.npmmirror.com

# 切换到项目根目录

# 安装插件
npm install

# 启动项目
npm run dev
```

# 蓝藻水华后台ui部署

    npm install --unsafe-perm --registry=https://registry.npmmirror.com

    docker buildx build --platform linux/amd64 -f Dockerfile -t ccr.ccs.tencentyun.com/game-center/bga-admin-ui:prod_1 .
    docker push ccr.ccs.tencentyun.com/game-center/bga-admin-ui:prod_1

    docker build -f Dockerfile -t cigit-unicorn-2023/bga-admin-ui:prod_v1 .

## local

  docker login 172.16.88.12:8088 -u ucas -p Harbor123456
  docker login ccr.ccs.tencentyun.com --username=100010627116 -p mustang123456

  docker buildx build --platform linux/amd64 -f Dockerfile -t ccr.ccs.tencentyun.com/game-center/bga-admin-ui:staging_v0.0.1 .
  docker build -f Dockerfile -t ccr.ccs.tencentyun.com/game-center/bga-admin-ui:staging_v0.0.2 .
  
  docker push ccr.ccs.tencentyun.com/game-center/bga-admin-ui:staging_v0.0.2
