<script setup lang="ts">
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import ContrastImage from "@/components/ContrastImage/ContrastImage.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataForm = reactive({
  id: "",
  url: "",
  urlRes: ""
});

const init = (id: string) => {
  dataForm.id = "";
  getPictureDetails(id);
};
// 获取图片详情
function getPictureDetails(id: string) {
  baseService.get(`/oss/files/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
    visible.value = true;
  });
}
defineExpose({
  init
});
</script>

<template>
  <el-dialog fullscreen center align-center v-model="visible" :title="$t('bga.comparePreview')" :close-on-click-modal="false" :close-on-press-escape="false">
    <contrast-image :url="dataForm.url" :urlRes="dataForm.urlRes"> </contrast-image>
  </el-dialog>
</template>
