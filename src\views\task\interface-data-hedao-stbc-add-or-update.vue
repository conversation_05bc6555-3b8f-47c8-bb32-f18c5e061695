<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('zhdc.stationCode')" prop="stationCode">
        <el-input v-model="dataForm.stationCode" :placeholder="$t('zhdc.stationCode')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.stationName')" prop="stationName">
        <el-input v-model="dataForm.stationName" :placeholder="$t('zhdc.stationName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.ammoniaNitrogen')" prop="ammoniaNitrogen">
        <el-input v-model="dataForm.ammoniaNitrogen" :placeholder="$t('zhdc.ammoniaNitrogen')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.totalPhosphorus')" prop="totalPhosphorus">
        <el-input v-model="dataForm.totalPhosphorus" :placeholder="$t('zhdc.totalPhosphorus')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.monitorTime')" prop="monitorTime">
        <el-input v-model="dataForm.monitorTime" :placeholder="$t('zhdc.monitorTime')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.belongingRiver')" prop="belongingRiver">
        <el-input v-model="dataForm.belongingRiver" :placeholder="$t('zhdc.belongingRiver')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.chemicalOxygenDemand')" prop="chemicalOxygenDemand">
        <el-input v-model="dataForm.chemicalOxygenDemand" :placeholder="$t('zhdc.chemicalOxygenDemand')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('zhdc.totalNitrogen')" prop="totalNitrogen">
        <el-input v-model="dataForm.totalNitrogen" :placeholder="$t('zhdc.totalNitrogen')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationCode: "",
  stationName: "",
  ammoniaNitrogen: "",
  totalPhosphorus: "",
  monitorTime: "",
  belongingRiver: "",
  chemicalOxygenDemand: "",
  totalNitrogen: ""
});

const rules = ref({
  stationCode: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  stationName: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  ammoniaNitrogen: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  totalPhosphorus: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  monitorTime: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  belongingRiver: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  chemicalOxygenDemand: [{ required: false, message: $t("validate.required"), trigger: "blur" }],
  totalNitrogen: [{ required: false, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/task/interface_data_hedao_stbc/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/task/interface_data_hedao_stbc", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
