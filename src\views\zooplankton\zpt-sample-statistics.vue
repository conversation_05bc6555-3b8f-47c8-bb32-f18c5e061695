<template>
  <div>
    <el-container class="layout-container-demo" style="height: 650px" v-loading="sampling.dataListLoading">
      <el-aside width="300px">
        <el-scrollbar>
          <el-form :inline="true" :model="sampling.dataForm" @keyup.enter="sampling.getDataList()">
            <el-form-item>
              <el-input style="width: 300px" v-model="sampling.dataForm.samplingLocation" :placeholder="$t('zooplankton.samplingLocation')" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker style="width: 300px" v-model="sampling.dataForm.samplingAt" format="YYYY-MM-DD" type="date" :placeholder="$t('zooplankton.samplingAt')" />
            </el-form-item>
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <el-button @click="sampling.getDataList()">{{ $t("query") }}</el-button>
              <el-button type="primary" @click="addOrUpdateHandleData()">{{ $t("add") }}</el-button>
            </div>
          </el-form>
          <div class="scrollbar" style="width: 100%">
            <el-card :class="{ active: selectSample === item.stationId }" v-for="item in sampling.dataList" @click.stop="getSamplingInfo(item)" shadow="hover" :key="+item.id" :body-style="{ padding: '20px 0', width: '100%' }">
              <div class="card-header">
                <span>{{ $t("zooplankton.sampleDataId") }}: {{ item.stationId }}</span>
                <div class="card-button">
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'detail')">{{ $t("details") }}</el-button>
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'update')">{{ $t("update") }}</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </el-scrollbar>
      </el-aside>
      <el-container v-if="samplingInfo.id">
        <el-header height="35%">
          <el-descriptions :title="$t('zooplankton.sampleDataInfo')">
            <el-descriptions-item :label="$t('zooplankton.samplingLocation')">
              <el-tag> {{ samplingInfo.samplingLocation }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.sampleVolumeLiters')">{{ samplingInfo.sampleVolumeLiters }}(L)</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.concentratedVolume')">{{ samplingInfo.concentratedVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.dilutionFactor')">{{ samplingInfo.dilutionFactor }}</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.fieldOfViewCount')">{{ samplingInfo.fieldOfViewCount ? samplingInfo.fieldOfViewCount + "(个)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.fieldArea')">{{ samplingInfo.fieldArea ? samplingInfo.fieldArea + "(mm2)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.countingArea')">{{ samplingInfo.countingArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.countingFrameVolume')">{{ samplingInfo.countingFrameVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.countingFrameArea')">{{ samplingInfo.countingFrameArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('zooplankton.samplingAt')">
              <el-tag type="success"> {{ samplingInfo.samplingAt }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('数据来源')">
              <el-tag type="warning"> {{ samplingInfo.dataSource }} </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-header>
        <el-main>
          <!-- 固定查询框和按钮 -->
          <div class="sticky-search-box">
            <el-form :inline="true" :model="samplingTestData.dataForm" @keyup.enter="samplingTestData.getDataList()">
              <el-form-item>
                <el-form-item>
                  <el-input style="width: 200px" v-model="samplingTestData.dataForm.zptName" :placeholder="$t('zooplankton.species')" clearable></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button @click="samplingTestData.getDataList()">{{ $t("query") }}</el-button>
                </el-form-item>
                <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="danger" @click="samplingTestData.deleteHandle()">{{ $t("delete") }}</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 固定表头的表格 -->
          <el-table
            v-loading="samplingTestData.dataListLoading"
            :data="samplingTestData.dataList"
            border
            @selection-change="samplingTestData.dataListSelectionChangeHandle"
            style="width: 100%"
            height="250"
          >
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="sampleId" :label="$t('zooplankton.sampleDataId')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="waterbodyName" :label="$t('zooplankton.waterbodyName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="monitorName" :label="$t('zooplankton.monitorName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="monitorUnit" :label="$t('zooplankton.monitorUnit')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeYear" :label="$t('zooplankton.timeYear')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeMonth" :label="$t('zooplankton.timeMonth')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDay" :label="$t('zooplankton.timeDay')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDetail" :label="$t('zooplankton.timeDetail')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingVolume" :label="$t('zooplankton.samplingVolume')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="zptPhylum" :label="$t('zooplankton.zptPhylum')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="zptClass" :label="$t('zooplankton.zptClass')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zptOrder" :label="$t('zooplankton.zptOrder')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zptFamily" :label="$t('zooplankton.zptFamily')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zptGenus" :label="$t('zooplankton.zptGenus')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zptName" :label="$t('zooplankton.zptName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zptLatin" :label="$t('zooplankton.zptLatin')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="density" :label="$t('zooplankton.density')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="wetWeight" :label="$t('zooplankton.wetWeight')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="biomass" :label="$t('zooplankton.biomass')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="notes" :label="$t('zooplankton.notes')" header-align="center" align="center"></el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="samplingTestData.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="samplingTestData.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="samplingTestData.limit"
            :total="samplingTestData.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="samplingTestData.pageSizeChangeHandle"
            @current-change="samplingTestData.pageCurrentChangeHandle"
          ></el-pagination>
        </el-main>
      </el-container>
      <el-container style="justify-content: center" v-else> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
    </el-container>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="samplingTestData.getDataList"></add-or-update>
    <show-or-update ref="showOrUpdateRef" @refreshDataList="sampling.getDataList"></show-or-update>
    <add-or-update-sample ref="addOrUpdateSampleRef" @refreshDataList="sampling.getDataList"></add-or-update-sample>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { onMounted, reactive, ref, toRefs } from "vue";
  import ShowOrUpdate from "./sampling-show-or-update.vue";
  import AddOrUpdate from "./zptdatanew-add-or-update.vue";
  import AddOrUpdateSample from "./zptsampledata-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import baseService from "@/service/baseService";
  const { $t } = globalLanguage();
  const view1 = reactive({
    deleteIsBatch: true,
    getDataListURL: "/zooplankton/zptsampledata/page",
    getDataListIsPage: true,
    exportURL: "/zooplankton/zptsampledata/export",
    deleteURL: "/zooplankton/zptsampledata",
    dataForm: {
      id: "",
      samplingLocation: "",
      createDate: "",
      samplingAt:"",
      zptName:"",
    }
  });
  const view2 = reactive({
    deleteIsBatch: true,
    getDataListURL: "/zooplankton/zptdatanew/page",
    getDataListIsPage: true,
    exportURL: "/zooplankton/zptdatanew/export",
    deleteURL: "/zooplankton/zptdatanew",
    dataForm: {
      sampleId: 0,
      species: "",
      zptName:"",
    }
  });
  onMounted(() => {
    baseService.get("/zooplankton/zptsampledata/page").then(({ data }) => {
      const firstData = data.list.filter((item: any) => item.stationId === selectSample.value);
      Object.assign(samplingInfo.value, firstData[0]);
      view2.dataForm.sampleId = selectSample.value;
      samplingTestData.getDataList();
    });
  });
  const samplingTestData = reactive({ ...useView(view2), ...toRefs(view2) });
  const samplingInfo = ref({
    id: "",
    stationId: "",
    samplingLocation: "",
    sampleVolumeLiters: "",
    concentratedVolume: "",
    dilutionFactor: "",
    fieldOfViewCount: "",
    fieldArea: "",
    countingArea: "",
    countingFrameVolume: "",
    countingFrameArea: "",
    samplingAt: "",
    createdAt: "",
    updateAt: "",
    deleteAt: "",
    dataSource: ""
  });
  const sampling = reactive({ ...useView(view1), ...toRefs(view1) });
  const selectSample = ref(1);
  const getSamplingInfo = (info: any) => {
    selectSample.value = info.zptName;
    Object.assign(samplingInfo.value, info);
    view2.dataForm.sampleId = info.stationId;
    samplingTestData.getDataList();
  };
  const showOrUpdateRef = ref();
  const showOrUpdateHandle = (id: any, type: string) => {
    showOrUpdateRef.value.init(id, type);
  };
  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const addOrUpdateSampleRef = ref();
  const addOrUpdateHandleData = (id?: number) => {
    addOrUpdateSampleRef.value.init(id);
  };
</script>

<style scoped lang="less">
  @import "../../assets/theme/base.less";
  .card-header {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .text {
    font-size: 14px;
  }
  .item {
    margin-bottom: 18px;
  }
  .scrollbar {
    display: flex;
    flex-direction: column;
  }

  .el-scrollbar__view .el-card:hover {
    color: @light-text-active;
  }

  .el-scrollbar__view .el-card.active {
    background: @light-bg-active;
    border: 1px solid @light-text-active;
    color: @light-text-active;
  }
  .el-card {
    cursor: pointer;
    border-radius: 16px;
    width: 100%;
    margin-top: 5%;
  }

  /* 固定查询框的样式 */
  .sticky-search-box {
    position: sticky;
    top: 0;
    background: white;
    z-index: 100;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }
</style>
