<template>
  <div class="mod-bga__zptclass">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptclass:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptclass:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nameCn" :label="$t('zooplankton.nameCn')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nameLatin" :label="$t('zooplankton.nameLatin')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="creator" :label="$t('zooplankton.creator')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="updater" :label="$t('zooplankton.updater')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('zooplankton.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="updateAt" :label="$t('zooplankton.updateAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="deleteAt" :label="$t('zooplankton.deleteAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('zooplankton.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('zooplankton:zptclass:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('zooplankton:zptclass:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zptclass-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/zooplankton/zptclass/page",
  getDataListIsPage: true,
  exportURL: "/zooplankton/zptclass/export",
  deleteURL: "/zooplankton/zptclass"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
