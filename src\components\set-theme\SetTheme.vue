<template>
  <el-drawer class="rr-setting-wrap" v-model="isShow" :before-close="handleClose" :destroy-on-close="true" :title="$t('theme.themeConfig')" modal :size="300">
    <div class="rr-setting">
      <div class="el-space el-space--vertical rr-theme" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" @click="changeSiderColor(item)" v-for="item in siderColor" style="padding-bottom: 0px; margin-right: 8px">
              <el-tooltip class="box-item" effect="dark" :content="item.tooltip" placement="top">
                <span :class="[item.color, { active: item.active }]" class="card side el-tooltip__trigger el-tooltip__trigger"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" @click="changeToppHeader(item)" v-for="item in topHeaderStyle" style="padding-bottom: 0px; margin-right: 8px">
              <el-tooltip class="box-item" effect="dark" :content="item.tooltip" placement="top">
                <span :class="[item.color, { active: item.active }]" class="card header el-tooltip__trigger el-tooltip__trigger"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="flex-wrap: wrap; margin-bottom: -2px; align-items: center">
            <div class="el-space__item" v-for="item in themeColor" style="padding-bottom: 2px; margin-right: 2px" @click="changeThemeColor(item)">
              <el-tooltip class="box-item" effect="dark" :content="item.tooltip" placement="top">
                <span :class="{ active: item.active }" class="color el-tooltip__trigger el-tooltip__trigger" :style="{ backgroundColor: item.color }"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div class="el-divider el-divider--horizontal" role="separator" style="--el-border-style: solid"><!--v-if--></div>
      <div class="el-space el-space--vertical rr-theme" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px">
          <span class="rr-setting-title text-2">{{ $t("theme.layoutModes") }}</span>
        </div>
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" v-for="item in navLayout" style="padding-bottom: 0px; margin-right: 8px" @click="changeNavLayout(item)">
              <el-tooltip class="box-item" effect="dark" :content="item.tooltip" placement="top">
                <span :class="[item.class, { active: item.active }]" class="card navlayout dark el-tooltip__trigger el-tooltip__trigger"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <span>{{ $t("theme.contentCover") }}</span>
            </div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="state.contentFull" @change="handleChangeFull" />
            </div>
          </div>
        </div>
      </div>
      <div class="el-divider el-divider--horizontal" role="separator" style="--el-border-style: solid"><!--v-if--></div>
      <div class="el-space el-space--vertical rr-other" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <span class="rr-setting-title text-2">{{ $t("theme.otherConfig") }}</span>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <span>{{ $t("theme.fixedLogoBar") }}</span>
            </div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="state.logoAuto" @change="handleChangeLogoAuto" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <span>{{ $t("theme.colorfulSidebarIcons") }}</span>
            </div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="state.colorIcon" @change="handleChangeColorIcon" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <span>{{ $t("theme.exclusiveExpandSidebar") }}</span>
            </div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="state.sidebarUniOpened" @change="handleChangeSiderUniOpened" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <span>{{ $t("theme.tagDisplayStyle") }}</span>
            </div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-select v-model="tagStyle" class="m-2" :placeholder="$t('theme.tagStyleDefault')" style="max-width: 80px" @change="handleChangeTag">
                <el-option v-for="item in tagSelect" :key="item.id" :label="item.name" :value="item.value" />
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { EMitt, EThemeColor, EThemeSetting } from "@/constants/enum";
import { getThemeConfigCache, getThemeConfigCacheByKey, setThemeColor, setThemeConfigToCache, updateTheme } from "@/utils/theme";
import emits from "@/utils/emits";
import { useRoute } from "vue-router";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const state = reactive({
  collapseSidebar: getThemeConfigCacheByKey(EThemeSetting.SidebarCollapse),
  colorIcon: getThemeConfigCacheByKey(EThemeSetting.ColorIcon),
  contentFull: getThemeConfigCacheByKey(EThemeSetting.ContentFull),
  logoAuto: getThemeConfigCacheByKey(EThemeSetting.LogoAuto),
  navLayout: getThemeConfigCacheByKey(EThemeSetting.NavLayout),
  openTabsPage: getThemeConfigCacheByKey(EThemeSetting.OpenTabsPage),
  sidebar: getThemeConfigCacheByKey(EThemeSetting.Sidebar),
  sidebarUniOpened: getThemeConfigCacheByKey(EThemeSetting.SidebarUniOpened),
  tabStyle: getThemeConfigCacheByKey(EThemeSetting.TabStyle),
  themeColor: getThemeConfigCacheByKey(EThemeSetting.ThemeColor),
  topHeader: getThemeConfigCacheByKey(EThemeSetting.TopHeader)
});
const route = useRoute();
let isShow = ref(false);
// 侧边栏
const siderColor = ref([
  {
    color: "dark",
    tooltip: $t("theme.darkSidebar"),
    active: state.sidebar === "dark"
  },
  {
    color: "light",
    tooltip: $t("theme.lightSidebar"),
    active: state.sidebar === "light"
  }
]);
// 头部栏
const topHeaderStyle = ref([
  {
    color: "dark",
    tooltip: $t("theme.darkTopBar"),
    active: state.topHeader === "dark"
  },
  {
    color: "light",
    tooltip: $t("theme.lightTopBar"),
    active: state.topHeader === "light"
  },
  {
    color: "primary",
    tooltip: $t("theme.themedTopBar"),
    active: state.topHeader === "primary"
  }
]);
// 主题色栏
const themeColor = ref([
  {
    color: "#409eff",
    tooltip: $t("theme.DawnBlue"),
    active: state.themeColor === "#409eff"
  },
  {
    color: "#0BB2D4",
    tooltip: $t("theme.Cyan"),
    active: state.themeColor === "#0BB2D4"
  },
  {
    color: "#3E8EF7",
    tooltip: $t("theme.Blue"),
    active: state.themeColor === "#3E8EF7"
  },
  {
    color: "#11C26D",
    tooltip: $t("theme.Green"),
    active: state.themeColor === "#11C26D"
  },
  {
    color: "#17B3A3",
    tooltip: $t("theme.BlueGreen"),
    active: state.themeColor === "#17B3A3"
  },
  {
    color: "#667AFA",
    tooltip: $t("theme.Indigo"),
    active: state.themeColor === "#667AFA"
  },
  {
    color: "#997B71",
    tooltip: $t("theme.Brown"),
    active: state.themeColor === "#997B71"
  },
  {
    color: "#9463F7",
    tooltip: $t("theme.Purple"),
    active: state.themeColor === "#9463F7"
  },
  {
    color: "#757575",
    tooltip: $t("theme.Gray"),
    active: state.themeColor === "#757575"
  },
  {
    color: "#EB6709",
    tooltip: $t("theme.Orange"),
    active: state.themeColor === "#EB6709"
  },
  {
    color: "#F74584",
    tooltip: $t("theme.MixedRed"),
    active: state.themeColor === "#F74584"
  },
  {
    color: "#FCB900",
    tooltip: $t("theme.Yellow"),
    active: state.themeColor === "#FCB900"
  },
  {
    color: "#FF4C52",
    tooltip: $t("theme.Red"),
    active: state.themeColor === "#FF4C52"
  },
  {
    color: "#141414",
    tooltip: $t("theme.Black"),
    active: state.themeColor === "#141414"
  }
]);

// 菜单栏布局
const navLayout = ref([
  {
    layout: "left",
    tooltip: $t("theme.leftMenuLayout"),
    active: state.navLayout === "left",
    class: "side"
  },
  {
    layout: "top",
    tooltip: $t("theme.topMenuLayout"),
    active: state.navLayout === "top",
    class: "header"
  },
  {
    layout: "mix",
    tooltip: $t("theme.mixedMenuLayout"),
    active: state.navLayout === "mix",
    class: "header mix"
  }
]);
const tagSelect = ref([
  { id: 1, name: $t("theme.tagStyleDefault"), value: "default" },
  { id: 2, name: $t("theme.tagStyleDot"), value: "round" },
  { id: 3, name: $t("theme.tagStyleCard"), value: "card" }
]);
const tagStyle = ref(state.tabStyle); // 设置为数组的第一个元素的值
// 改变侧边栏样式
const changeSiderColor = (siderItem: any) => {
  siderColor.value.forEach((item) => {
    item.active = item.color === siderItem.color; // 将点击的颜色设置为 active
  });
  siderColor.value.forEach((item) => {
    if (item.color !== siderItem.color) {
      item.active = false;
    }
  });
  state.sidebar = siderItem.color;
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.Sidebar, EThemeSetting.Sidebar + "-" + state.sidebar]);
  setThemeConfigToCache(EThemeSetting.Sidebar, state.sidebar);
};
// 改变状态栏样式
const changeToppHeader = (topItem: any) => {
  topHeaderStyle.value.forEach((item) => {
    item.active = item.color === topItem.color; // 将点击的颜色设置为 active
  });
  topHeaderStyle.value.forEach((item) => {
    if (item.color !== topItem.color) {
      item.active = false;
    }
  });
  state.topHeader = topItem.color;
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.TopHeader, EThemeSetting.TopHeader + "-" + state.topHeader]);
  setThemeConfigToCache(EThemeSetting.TopHeader, state.topHeader);
};
// 改变主题色样式
const changeThemeColor = (themeItem: any) => {
  themeColor.value.forEach((item) => {
    item.active = item.color === themeItem.color; // 将点击的颜色设置为 active
  });
  themeColor.value.forEach((item) => {
    if (item.color !== themeItem.color) {
      item.active = false;
    }
  });
  state.themeColor = themeItem.color;
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.ThemeColor, EThemeSetting.ThemeColor + "-" + state.themeColor]);
  setThemeConfigToCache(EThemeSetting.ThemeColor, state.themeColor);
  const themeCache = getThemeConfigCache();
  const themeColorConfig = themeCache[EThemeSetting.ThemeColor];
  setThemeColor(EThemeColor.ThemeColor, themeColorConfig);
  updateTheme(themeColorConfig);
};
// 改变菜单栏样式
const changeNavLayout = (nav: any) => {
  console.log(nav);

  navLayout.value.forEach((item) => {
    item.active = item.layout === nav.layout;
  });
  navLayout.value.forEach((item) => {
    if (item.layout !== nav.layout) {
      item.active = false;
    }
  });
  state.navLayout = nav.layout;
  emits.emit(EMitt.OnSetNavLayout);
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.NavLayout, EThemeSetting.NavLayout + "-" + state.navLayout]);
  setThemeConfigToCache(EThemeSetting.NavLayout, state.navLayout);
  location.reload();
};
// 改变内容区域
const handleChangeFull = () => {
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.ContentFull, EThemeSetting.ContentFull + "-" + state.contentFull]);
  setThemeConfigToCache(EThemeSetting.ContentFull, state.contentFull);
  if (route.path === "/home") {
    emits.emit(EMitt.OnReloadTabPage);
  }
};
// 改变logo自动
const handleChangeLogoAuto = () => {
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.LogoAuto, EThemeSetting.LogoAuto + "-" + state.logoAuto]);
  setThemeConfigToCache(EThemeSetting.LogoAuto, state.logoAuto);
  if (route.path === "/home") {
    emits.emit(EMitt.OnReloadTabPage);
  }
};
// 改变彩色字体
const handleChangeColorIcon = () => {
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.ColorIcon, EThemeSetting.ColorIcon + "-" + state.colorIcon]);
  setThemeConfigToCache(EThemeSetting.ColorIcon, state.colorIcon);
};
// 改变侧边栏展开
const handleChangeSiderUniOpened = () => {
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.SidebarUniOpened, EThemeSetting.SidebarUniOpened + "-" + state.sidebarUniOpened]);
  setThemeConfigToCache(EThemeSetting.SidebarUniOpened, state.sidebarUniOpened);
};
const handleChangeTag = (value: any) => {
  state.tabStyle = value;
  emits.emit(EMitt.OnSetTheme, [EThemeSetting.TabStyle, EThemeSetting.TabStyle + "-" + state.tabStyle]);
  setThemeConfigToCache(EThemeSetting.TabStyle, state.tabStyle);
};
const isOpen = () => {
  isShow.value = true;
};
const isClose = () => {
  isShow.value = false;
};
defineExpose({
  isOpen,
  isClose
});
const handleClose = () => {
  isClose();
};
</script>

<style>
.rr-setting {
  padding: 20px;
}

.rr-setting .el-divider {
  margin: 20px 0;
}
.el-drawer__header .el-drawer__title {
  font-size: 1.5em;
}
.rr-setting-wrap .el-drawer__header {
  color: #595959;
  font-size: 15px;
  margin-bottom: 0;
  padding: 13px 16px;
  border-bottom: 1px solid #f4f4f4;
}

.rr-setting-wrap .el-drawer__body {
  overflow: auto;
  padding: 0;
}

.rr-setting-title {
  font-size: 13px;
}

.rr-setting .rr-theme .card {
  width: 50px;
  height: 35px;
  border-radius: 3px;
  margin: 0 20px 20px 0;
  background-color: #f5f7fa;
  box-shadow: 0 1px 3px #00000026;
  display: inline-block;
  vertical-align: top;
  position: relative;
  cursor: pointer;
}

.rr-setting .rr-theme .card.side:before {
  content: "";
  width: 15px;
  height: 100%;
  background-color: #fff;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  display: inline-block;
  vertical-align: top;
}

.rr-setting .rr-theme .card.side.dark:before {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.header:before {
  content: "";
  border-top-left-radius: 3px;
  display: inline-block;
  vertical-align: top;
  width: 100%;
  height: 10px;
  background-color: #fff;
  border-bottom-left-radius: 0;
  border-top-right-radius: 3px;
}

.rr-setting .rr-theme .card.header.light:before {
  width: 100%;
  height: 10px;
  background-color: #fff;
  border-bottom-left-radius: 0;
  border-top-right-radius: 3px;
}

.rr-setting .rr-theme .card.header.dark:before {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.header.primary:before {
  background-color: #409eff;
}

.rr-setting .rr-theme .card.mix {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.mix.dark:before {
  background-color: #f0f2f5;
  width: 35px;
  height: 25px;
  position: absolute;
  bottom: 0;
  right: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 3px;
}

.rr-setting .rr-theme .card.side.active:after,
.rr-setting .rr-theme .card.header.active:after,
.rr-setting .rr-theme .card.mix.active:after {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #19be6b;
  position: absolute;
  left: 50%;
  bottom: -15px;
  margin-left: -3px;
}

.rr-setting .rr-theme .color {
  width: 20px;
  height: 20px;
  margin: 8px 8px 0 0;
  border-radius: 2px;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0 0 0, 0.1);
  vertical-align: top;
  position: relative;
  cursor: pointer;
}

.rr-setting .rr-theme .color.active:after {
  content: url('data:image/svg+xml;charset=utf-8,<svg width="14" height="14" color="rgb(255 255 255)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M406.656 706.944L195.84 496.256a32 32 0 10-45.248 45.248l256 256 512-512a32 32 0 00-45.248-45.248L406.592 706.944z"></path></svg>');
  font-family: element-icons !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -7px 0 0 -7px;
  font-size: 14px;
  color: #fff;
}

.rr-setting .rr-theme,
.rr-setting .rr-other {
  width: 100%;
}

.rr-setting .rr-theme > .el-space__item,
.rr-setting .rr-other > .el-space__item {
  width: 100%;
  position: relative;
}

.rr-setting .rr-switch {
  justify-content: space-between;
  width: 100%;
}
</style>
