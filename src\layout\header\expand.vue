<script lang="ts">
import SvgIcon from "@/components/base/svg-icon";
import baseService from "@/service/baseService";
import { useFullscreen } from "@vueuse/core";
import { defineComponent } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "@/store";
import userLogo from "@/assets/images/user.png";
import "@/assets/css/header.less";
import SetTheme from "@/components/set-theme/SetTheme.vue";
import { ElMessageBox } from "element-plus";
import { ref } from "vue";
import i18n from "@/i18n";
import { Language } from "@/types/interface";
interface IExpand {
  userName?: string;
}

/**
 * 顶部右侧扩展区域
 */
export default defineComponent({
  name: "Expand",
  components: { SvgIcon, SetTheme },
  props: {
    userName: String
  },
  setup(props: IExpand) {
    const router = useRouter();
    const store = useAppStore();
    const { isFullscreen, toggle } = useFullscreen();
    const showDrawer = ref(false);
    const setTheme = () => {
      drawer.value.isOpen();
    };
    const drawer = ref();
    const onClickUserMenus = (path: string) => {
      if (path === "/login") {
        ElMessageBox.confirm("确定进行[退出]操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            baseService.post("/logout").finally(() => {
              router.push(path);
            });
          })
          .catch(() => {
            //
          });
      } else {
        router.push(path);
      }
    };
    const handleChangeLang = (lang: Language) => {
      if (i18n.global.locale.value === lang) return; // 切换语言
      i18n.global.locale.value = lang;
      localStorage.setItem("language", lang); // 将语言设置存储到 localStorage
      location.reload(); // 刷新页面
    };
    return {
      props,
      store,
      isFullscreen,
      userLogo,
      onClickUserMenus,
      toggle,
      setTheme,
      showDrawer,
      drawer,
      handleChangeLang
    };
  }
});
</script>
<template>
  <div class="rr-header-right-items">
    <div @click="toggle" class="hidden-xs-only">
      <span>
        <svg-icon :name="isFullscreen ? 'tuichuquanping' : 'fullscreen2'"></svg-icon>
      </span>
    </div>
    <div>
      <span class="rr-login-right-main-lang">
        <el-dropdown @command="handleChangeLang">
          <span class="el-dropdown-link">
            <svg aria-hidden="true" class="iconfont el-tooltip__trigger" id="el-id-5038-76" role="button" tabindex="0" aria-controls="el-id-5038-77" aria-expanded="false" aria-haspopup="menu" data-v-8748f8d5=""><use xlink:href="#icon-fanyiline"></use></svg>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh-CN">简体中文</el-dropdown-item>
              <el-dropdown-item command="en-US">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </div>
    <div style="display: flex; justify-content: center; align-items: center">
      <img :src="userLogo" :alt="props.userName" style="width: 30px; height: 30px; border-radius: 50%; margin-top: 3px; margin-right: 5px" />
      <el-dropdown @command="onClickUserMenus">
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item icon="lock" command="/user/password"> {{ $t("updatePassword.title") }} </el-dropdown-item>
            <el-dropdown-item icon="switch-button" divided command="/login"> {{ $t("updatePassword.loginOut") }} </el-dropdown-item>
          </el-dropdown-menu>
        </template>
        <span class="el-dropdown-link" style="display: flex">
          {{ props.userName }}
          <el-icon class="el-icon--right" style="font-size: 14px"><arrow-down /></el-icon>
        </span>
      </el-dropdown>
    </div>
    <div @click="setTheme">
      <span class="rr-header-right-items-icon">
        <svg aria-hidden="true" class="iconfont" style=""><use xlink:href="#icon-morevertical"></use></svg>
      </span>
    </div>
  </div>
  <set-theme ref="drawer"></set-theme>
</template>
