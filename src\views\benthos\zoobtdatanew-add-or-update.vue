<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('benthos.samplingId')" prop="samplingId">
        <el-input v-model="dataForm.sampleId" :placeholder="$t('benthos.samplingId')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.waterbodyName')" prop="waterbodyName">
        <el-input v-model="dataForm.waterbodyName" :placeholder="$t('benthos.waterbodyName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.monitorName')" prop="monitorName">
        <el-input v-model="dataForm.monitorName" :placeholder="$t('benthos.monitorName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.monitorUnit')" prop="monitorUnit">
        <el-input v-model="dataForm.monitorUnit" :placeholder="$t('benthos.monitorUnit')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.timeYear')" prop="timeYear">
        <el-input v-model="dataForm.timeYear" :placeholder="$t('benthos.timeYear')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.timeMonth')" prop="timeMonth">
        <el-input v-model="dataForm.timeMonth" :placeholder="$t('benthos.timeMonth')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.timeDay')" prop="timeDay">
        <el-input v-model="dataForm.timeDay" :placeholder="$t('benthos.timeDay')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.timeDetail')" prop="timeDetail">
        <el-input v-model="dataForm.timeDetail" :placeholder="$t('benthos.timeDetail')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtPhylum')" prop="zoobtPhylum">
        <el-input v-model="dataForm.zoobtPhylum" :placeholder="$t('benthos.zoobtPhylum')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtClass')" prop="zoobtClass">
        <el-input v-model="dataForm.zoobtClass" :placeholder="$t('zooplankton.zoobtClass')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtOrder')" prop="zoobtOrder">
        <el-input v-model="dataForm.zoobtOrder" :placeholder="$t('zooplankton.zoobtOrder')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtFamily')" prop="zoobtFamily">
        <el-input v-model="dataForm.zoobtFamily" :placeholder="$t('zooplankton.zoobtFamily')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtGenus')" prop="zoobtGenus">
        <el-input v-model="dataForm.zoobtGenus" :placeholder="$t('zooplankton.zoobtGenus')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtName')" prop="zoobtName">
        <el-input v-model="dataForm.zoobtName" :placeholder="$t('zooplankton.zoobtName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.zoobtLatin')" prop="zoobtLatin">
        <el-input v-model="dataForm.zoobtLatin" :placeholder="$t('zooplankton.zoobtLatin')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.density')" prop="density">
        <el-input v-model="dataForm.density" :placeholder="$t('zooplankton.density')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.wetWeight')" prop="wetWeight">
        <el-input v-model="dataForm.wetWeight" :placeholder="$t('zooplankton.wetWeight')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.biomass')" prop="biomass">
        <el-input v-model="dataForm.biomass" :placeholder="$t('zooplankton.biomass')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('benthos.notes')" prop="notes">
        <el-input v-model="dataForm.notes" :placeholder="$t('zooplankton.notes')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  samplingId: "",
  waterbodyName: "",
  monitorName: "",
  monitorUnit: "",
  timeYear: "",
  timeMonth: "",
  timeDay: "",
  timeDetail: "",
  zoobtPhylum: "",
  zoobtClass: "",
  zoobtOrder: "",
  zoobtFamily: "",
  zoobtGenus: "",
  zoobtName: "",
  zoobtLatin: "",
  density: "",
  sampleId: "",
  wetWeight: "",
  biomass: "",
  notes: ""
});

const rules = ref({
  samplingId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  waterbodyName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  monitorName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  monitorUnit: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeYear: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeMonth: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDay: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  timeDetail: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtPhylum: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtClass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtOrder: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtFamily: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtGenus: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  zoobtLatin: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  density: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  wetWeight: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  biomass: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  notes: [{ required: false, message: $t("validate.required"), trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/benthos/zoobtdatanew/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/benthos/zoobtexcelupload", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

