<template>
  <div class="mod-bga__zptbioscale">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptbioscale:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('zooplankton:zptbioscale:delete')" type="danger" @click="state.deleteHandle()"> {{ $t("delete") }} </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zptId" :label="$t('zooplankton.zptId')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zptCategoryName" :label="$t('zooplankton.zptCategoryName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zptGenusName" :label="$t('zooplankton.zptGenusName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('zooplankton.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationName" :label="$t('zooplankton.stationName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="biomass" :label="$t('zooplankton.biomass')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('zooplankton.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="updateAt" :label="$t('zooplankton.updateAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="deleteAt" :label="$t('zooplankton.deleteAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('zooplankton.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('bga:zptbioscale:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('bga:zptbioscale:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }} </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zptbioscale-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/zooplankton/zptbioscale/page",
  getDataListIsPage: true,
  exportURL: "/zooplankton/zptbioscale/export",
  deleteURL: "/zooplankton/zptbioscale"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
