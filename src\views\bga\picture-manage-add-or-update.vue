<script setup lang="ts">
import { ref, reactive } from "vue";
import baseService from "@/service/baseService";
import { UploadFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useAppStore } from "@/store";
import emits from "@/utils/emits";
import type { UploadFile, UploadFiles } from "element-plus";
import { EMitt } from "@/constants/enum";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const store = useAppStore();
const addressList = ref(store.state.address.list);
const visible = ref(false);
const dataFormRef = ref();
const addFileRef = ref();
const dataForm = reactive({
  id: "",
  fileName: "",
  fileId: "",
  url: "",
  urlRes: "",
  memo: "",
  addressId: "",
  address: "",
  area: "",
  creator: "",
  createDate: ""
});
const addRules = ref({
  address: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  createDate: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});
const uploadRules = ref({
  url: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  urlRes: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  address: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});
const num = ref(0);
const uploadFiles = ref([]);
const uploadPath = ref("");
const init = (id = null) => {
  dataForm.id = "";
  if (!id) {
    visible.value = true;
    uploadFiles.value = [];
  }

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.keys(dataForm).forEach((key) => {
      if (key in dataForm) {
        (dataForm as any)[key] = "";
      }
    });
  }
  if (id) {
    getPictureDetails(id);
  }
};
// 获取图片详情
function getPictureDetails(id: string) {
  baseService.get(`/oss/files/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
    visible.value = true;
  });
}
defineExpose({
  init
});

// 文件上传前
function beforeUploadHandle() {
  num.value++;
}
// 成功上传
function uploadFileSuccess(response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) {}
// 确定
function dataFormSubmitHandle() {
  if (!dataForm.id) {
    addFileRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
    });
  }
  (!dataForm.id ? baseService.post : baseService.put)("", {
    ...dataForm
  }).then((res) => {
    ElMessage.success({
      message: $t("prompt.success"),
      duration: 1000,
      onClose: () => {
        visible.value = false;
        emit("refreshDataList");
      }
    });
  });
}
// 关闭
function closeDialogHandle() {
  uploadFiles.value = [];
  num.value = 0;
  Object.assign(dataForm, {
    id: "",
    fileName: "",
    fileId: "",
    url: "",
    urlRes: null,
    memo: null,
    addressId: "",
    address: "",
    area: 0.0,
    creator: null,
    createDate: ""
  });
  emits.emit(EMitt.OnReloadTabPage);
}
</script>

<template>
  <el-dialog @closed="closeDialogHandle" align-center v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="addFile" v-if="!dataForm.id">
      <el-form :model="dataForm" ref="addFileRef" :rules="addRules" label-width="auto" size="large" label-position="left">
        <el-form-item :label="$t('bga.address')" prop="address">
          <el-select clearble v-model="dataForm.address" value-key="id" :placeholder="$t('bga.address')" size="large"><el-option v-for="item in addressList" :key="item.id" :label="$t(`address.${item.address}`)" :value="item.id"></el-option> </el-select>
        </el-form-item>
        <el-form-item :label="$t('bga.selectDateTime')" prop="createDate">
          <el-date-picker style="width: 100%" v-model="dataForm.createDate" type="datetime" :placeholder="$t('bga.selectDateTime')"></el-date-picker>
        </el-form-item>
      </el-form>
      <el-upload :file-list="uploadFiles" ref="uploadRef" class="upload-demo" show-file-list :on-success="uploadFileSuccess" :before-upload="beforeUploadHandle" drag :action="uploadPath" multiple>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">{{ $t("upload.text") }}</div>
        <template #tip>
          <div class="el-upload__tip" style="text-align: center; font-size: 13px">{{ $t("upload.tip") }}</div>
        </template>
      </el-upload>
    </div>
    <div class="uploadFile" v-else>
      <el-form :model="dataForm" :rules="uploadRules" label-position="left" label-width="auto" size="large" ref="dataFormRef">
        <el-form-item :label="$t('bga.urlAddress')" header-align="center" align="center" prop="url">
          <el-input :placeholder="$t('bga.urlAddress')" v-model="dataForm.url"></el-input>
        </el-form-item>
        <el-form-item :label="$t('bga.segmentedResultData')" header-align="center" align="center" prop="urlRes">
          <el-input :placeholder="$t('bga.segmentedResultData')" v-model="dataForm.urlRes"></el-input>
        </el-form-item>
        <el-form-item :label="$t('bga.address')" header-align="center" align="center" prop="address">
          <el-input :placeholder="$t('bga.segmentedResultData')" v-model="dataForm.address"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>
