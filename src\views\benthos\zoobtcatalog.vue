<template>
  <div class="mod-bga__zoobtcatalog">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('benthos:zoobtcatalog:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('benthos:zoobtcatalog:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtPhylumName" :label="$t('benthos.zoobtPhylumName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtPhylumLatinName" :label="$t('benthos.zoobtPhylumLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtClassName" :label="$t('benthos.zoobtClassName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtClassLatinName" :label="$t('benthos.zoobtClassLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtOrderName" :label="$t('benthos.zoobtOrderName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtOrderLatinName" :label="$t('benthos.zoobtOrderLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtFamilyName" :label="$t('benthos.zoobtFamilyName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtFamilyLatinName" :label="$t('benthos.zoobtFamilyLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtGenusName" :label="$t('benthos.zoobtGenusName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtGenusLatinName" :label="$t('benthos.zoobtGenusLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtSpeciesName" :label="$t('benthos.zoobtSpeciesName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zoobtSpeciesLatinName" :label="$t('benthos.zoobtSpeciesLatinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('benthos.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="updateAt" :label="$t('benthos.updateAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="deleteAt" :label="$t('benthos.deleteAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('benthos.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('benthos:zoobtcatalog:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('benthos:zoobtcatalog:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zoobtcatalog-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/benthos/zoobtcatalog/page",
  getDataListIsPage: true,
  exportURL: "/benthos/zoobtcatalog/export",
  deleteURL: "/benthos/zoobtcatalog"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
