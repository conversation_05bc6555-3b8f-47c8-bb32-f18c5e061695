<template>
  <div class="mod-bga__phytodata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="uploadBgaDataHandle()">{{ $t("zooplankton.uploadDataTable") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="samplingId" :label="$t('zooplankton.sampleDataId')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="samplingLocation" :label="$t('zooplankton.samplingLocation')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="category" :label="$t('zooplankton.category')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="species" :label="$t('zooplankton.species')" header-align="center" align="center"></el-table-column>
      <el-table-column width="200" prop="latinName" :label="$t('zooplankton.latinName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="wetWeight" :label="$t('zooplankton.wetWeight')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="count" :label="$t('zooplankton.count')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="cellCount" :label="$t('zooplankton.cellCount')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="biomass" :label="$t('zooplankton.biomass')" header-align="center" align="center"></el-table-column>
      <el-table-column width="160" prop="createdAt" :label="$t('zooplankton.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="160" prop="updateAt" :label="$t('zooplankton.updateAt')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadFile" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zptdata-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/zooplankton/zptdata/page",
  getDataListIsPage: true,
  exportURL: "/zooplankton/zptdata/export",
  deleteURL: "/zooplankton/zptdata",
  uploadFile: "/zooplankton/zptdata/upload"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};
</script>
