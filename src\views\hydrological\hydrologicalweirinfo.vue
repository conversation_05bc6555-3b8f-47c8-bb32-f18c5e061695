<template>
  <div class="mod-bga__hydrologicalweirinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('hydrological:hydrologicalweirinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('hydrological:hydrologicalweirinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('hydrological.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="waterTemperature" :label="$t('hydrological.waterTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="uswl" :label="$t('hydrological.uswl')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dswl" :label="$t('hydrological.dswl')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="flowVelocity" :label="$t('hydrological.flowVelocity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="flowRate" :label="$t('hydrological.flowRate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="flowMeasMethod" :label="$t('hydrological.flowMeasMethod')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="createdAt" :label="$t('hydrological.createdAt')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="remark" :label="$t('hydrological.remark')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('hydrological:hydrologicalweirinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('hydrological:hydrologicalweirinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./hydrologicalweirinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/hydrological/hydrologicalweirinfo/page",
  getDataListIsPage: true,
  exportURL: "/hydrological/hydrologicalweirinfo/export",
  deleteURL: "/hydrological/hydrologicalweirinfo"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
