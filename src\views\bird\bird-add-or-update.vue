<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('鸟类图像地址')" prop="imgPath">
        <el-input v-model="dataForm.imgPath" :placeholder="$t('鸟类图像地址')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('鸟类视频地址')" prop="mediaUrl">
        <el-input v-model="dataForm.mediaUrl" :placeholder="$t('鸟类视频地址')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('鸟类Id')" prop="speciesId">
        <el-input v-model="dataForm.speciesId" :placeholder="$t('鸟类Id')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('鸟类名称')" prop="speciesName">
        <el-input v-model="dataForm.speciesName" :placeholder="$t('鸟类名称')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('时间')" prop="time">
        <el-input v-model="dataForm.time" :placeholder="$t('时间')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('摄像头id')" prop="deviceId">
        <el-input v-model="dataForm.deviceId" :placeholder="$t('摄像头id')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const emit = defineEmits(["refreshDataList"]);

  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: "",
    imgPath:"",
    mediaUrl:"",
    speciesId:"",
    speciesName:"",
    time:"",
    deviceId:""
  });

  const rules = ref({
    imgUrl: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    birdNumber: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    birdId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    birdName: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
  });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";

    // 重置表单数据
    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  // 获取信息
  const getInfo = (id: number) => {
    baseService.get("bird/birdnumbercount/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  // 表单提交
  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("bird/birdnumbercount", dataForm).then((res) => {
        ElMessage.success({
          message: $t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    });
  };

  defineExpose({
    init
  });
</script>
