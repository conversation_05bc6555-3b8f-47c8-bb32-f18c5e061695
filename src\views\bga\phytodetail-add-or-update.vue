<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="80%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" label-width="auto">
      <el-form-item :label="$t('phytoplankto.name')" prop="name">
        <el-input v-model="dataForm.name" :placeholder="$t('phytoplankto.name')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.phylumName')" prop="phylumName">
        <el-select clearable v-model="dataForm.phylumName" value-key="id" :placeholder="$t('phytoplankto.phylumName')">
          <el-option v-for="item in phytophylumData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.className')" prop="className">
        <el-select clearable v-model="dataForm.className" value-key="id" :placeholder="$t('phytoplankto.className')">
          <el-option v-for="item in phytoclassData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameOrder')" prop="nameOrder">
        <el-select clearable v-model="dataForm.nameOrder" value-key="id" :placeholder="$t('phytoplankto.nameOrder')">
          <el-option v-for="item in phytoorderData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameFamily')" prop="nameFamily">
        <el-select clearable v-model="dataForm.nameFamily" value-key="id" :placeholder="$t('phytoplankto.nameFamily')">
          <el-option v-for="item in phytofamilyData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.nameGenus')" prop="nameGenus">
        <el-select clearable v-model="dataForm.nameGenus" value-key="id" :placeholder="$t('phytoplankto.nameGenus')">
          <el-option v-for="item in phytogenusData" :key="item.id" :label="item.nameCn" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.type')" prop="type">
        <el-input v-model="dataForm.type" :placeholder="$t('phytoplankto.type')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.identification')" prop="identification">
        <el-input v-model="dataForm.identification" :placeholder="$t('phytoplankto.identification')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.stationAddr')" prop="stationAddr">
        <el-input v-model="dataForm.stationAddr" :placeholder="$t('phytoplankto.stationAddr')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.autecology')" prop="autecology">
        <el-input v-model="dataForm.autecology" :placeholder="$t('phytoplankto.autecology')"></el-input>
      </el-form-item>
      <el-form-item label="选择图片" prop="img">
        <div class="upload-container">
          <el-upload
            class="upload-demo"
            :limit="1"
            accept="image/*"
            :show-file-list="false"
            :before-upload="handleFileChange"
          >
            <div class="upload-box">
              <i class="el-icon-plus"></i>
              <span>{{ $t('双击添加/修改图片') }}</span>
            </div>
          </el-upload>
          <div v-if="dataForm.img" class="image-container">
            <img :src="dataForm.img" alt="预览图片" class="preview-image" />
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.contributor')" prop="contributor">
        <el-input v-model="dataForm.contributor" :placeholder="$t('phytoplankto.contributor')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.reviewer')" prop="reviewer">
        <el-input v-model="dataForm.reviewer" :placeholder="$t('phytoplankto.reviewer')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('phytoplankto.status')" prop="status">
        <el-select clearable v-model="dataForm.status" value-key="id" :placeholder="$t('phytoplankto.status')">
          <el-option v-for="item in stateStatus" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('phytoplankto.description')" prop="description">
        <el-input v-model="dataForm.description" :placeholder="$t('phytoplankto.description')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('bga.remark')" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="$t('bga.remark')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from "vue";
  import baseService from "@/service/baseService";
  import { ElMessage } from "element-plus";
  import { Plus } from "@element-plus/icons-vue";
  import { phytoplanktonObject } from "@/types/interface";
  import type { UploadProps, UploadUserFile } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";

  const { $t } = globalLanguage();
  const visible = ref(false);
  const dataFormRef = ref();

  const dataForm = reactive({
    id: "",
    name: "",
    phylumName: "",
    className: "",
    nameOrder: "",
    nameFamily: "",
    nameGenus: "",
    type: "",
    identification: "",
    stationAddr: "",
    autecology: "",
    contributor: "",
    reviewer: "",
    status: "",
    description: "",
    remark: "",
    img: ""
  });

  const params = ref({
    page: 1,
    limit: 99999
  });
  // 数据状态
  const stateStatus = ref([
    { value: "0", label: "初始提交审核" },
    { value: "1", label: "审核通过" },
    { value: "2", label: "驳回" }
  ]);
  const phytophylumData = ref<phytoplanktonObject[]>([]); // 门
  const phytoclassData = ref<phytoplanktonObject[]>([]); // 纲
  const phytoorderData = ref<phytoplanktonObject[]>([]); // 目
  const phytofamilyData = ref<phytoplanktonObject[]>([]); // 科
  const phytogenusData = ref<phytoplanktonObject[]>([]); // 属

  // 获取下拉列表
  const getAllData = async () => {
    const [phytophylum, phytoclass, phytoorder, phytofamily, phytogenus] = await Promise.all([
      baseService.get("/bga/phytophylum/page", params.value),
      baseService.get("/bga/phytoclass/page", params.value),
      baseService.get("/bga/phytoorder/page", params.value),
      baseService.get("/bga/phytofamily/page", params.value),
      baseService.get("/bga/phytogenus/page", params.value)
    ]);
    phytophylumData.value = phytophylum.data.list;
    phytoclassData.value = phytoclass.data.list;
    phytoorderData.value = phytoorder.data.list;
    phytofamilyData.value = phytofamily.data.list;
    phytogenusData.value = phytogenus.data.list;
  };

  onMounted(() => {
    getAllData();
  });

  const rules = ref({
    name: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    phylumName: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    className: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    nameOrder: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    nameFamily: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    nameGenus: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    stationAddr: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
    status: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
  });

  const init = (id?: number) => {
    visible.value = true;
    dataForm.id = "";
    dataForm.img = "";

    if (dataFormRef.value) {
      dataFormRef.value.resetFields();
    }

    if (id) {
      getInfo(id);
    }
  };

  const getInfo = (id: number) => {
    baseService.get("/bga/phytodetail/" + id).then((res) => {
      Object.assign(dataForm, res.data);
    });
  };

  const dataFormSubmitHandle = () => {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      (!dataForm.id ? baseService.post : baseService.put)("/bga/phytodetail", dataForm).then((res) => {
        ElMessage.success({
          message: $t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
          }
        });
      });
    });
  };

  const handleFileChange = (file: File) => {
    if (!file.type.startsWith("image/")) {
      ElMessage.error("只能上传图片文件");
      return false;
    }

    if (file.size / 1024 / 1024 > 2) {
      ElMessage.error("图片大小不能超过 2MB");
      return false;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      dataForm.img = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    return false;
  };

  defineExpose({
    init
  });
</script>

<style scoped>
  .upload-container {
    position: relative;
    width: 100%;
    max-width: 300px;
  }

  .upload-demo {
    position: relative;
    width: 100%;
    height: 180px;
    border: 1px dashed var(--el-border-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--el-transition-duration-fast);
  }

  .upload-demo:hover {
    border-color: var(--el-color-primary);
  }

  .upload-box {
    text-align: center;
  }

  .upload-box i {
    font-size: 28px;
    color: #8c939d;
    margin-bottom: 8px;
  }

  .upload-box span {
    font-size: 14px;
    color: #8c939d;
  }

  .image-container {
    position: relative;
    margin-top: 10px;
  }

  .preview-image {
    max-width: 300px;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
</style>
