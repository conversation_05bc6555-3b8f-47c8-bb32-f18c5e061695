<template>
  <div class="mod-bga__phytoSampleStatistics">
    <el-container class="layout-container-demo" style="height: 800px" v-loading="sampling.dataListLoading">
      <el-aside width="500px">
        <el-scrollbar>
          <el-form :inline="true" :model="sampling.dataForm" @keyup.enter="sampling.getDataList()">
            <el-form-item>
              <el-input style="width: 300px" v-model="sampling.dataForm.samplingLocation" :placeholder="$t('phytoplankto.samplingLocation')" clearable></el-input>
            </el-form-item>

            <div class="container">
              <div class="block">
                <span class="demonstration"></span>
                <el-date-picker v-model="sampling.dataForm.samplingAt" type="year" placeholder="选择年"> </el-date-picker>
              </div>
              <div class="block">
                <span class="demonstration"></span>
                <el-date-picker v-model="sampling.dataForm.samplingAt" type="month" placeholder="选择月"> </el-date-picker>
              </div>
            </div>
            <div style="width: 100%; display: flex; justify-content: flex-end">
              <el-button @click="sampling.getDataList()">{{ $t("query") }}</el-button>
              <el-button type="primary" @click="addOrUpdateHandleData()">{{ $t("add") }}</el-button>
            </div>
          </el-form>
          <div class="scrollbar" style="width: 100%">
            <el-card :class="{ active: selectSample === item.stationId }" v-for="item in samplingTestData.dataList" @click.stop="getSamplingInfo(item)" shadow="hover" :key="+item.id" :body-style="{ padding: '20px 0', width: '100%' }">
              <div class="card-header">
                <span>{{ $t("phytoplankto.sampleDataId") }}: {{ item.stationId }} - {{ item.stationName }}</span>
                <div class="card-button">
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'detail')">{{ $t("details") }}</el-button>
                  <el-button class="button" type="primary" size="small" bg text @click.stop="showOrUpdateHandle(item.id, 'update')">{{ $t("update") }}</el-button>
                </div>
              </div>
            </el-card>

            <el-tree
              :data="treeData"
              :props="defaultProps"
              node-key="id"
              @node-click="handleNodeClick">
            </el-tree>

          </div>
        </el-scrollbar>
      </el-aside>
      <el-container v-if="samplingInfo.id">
        <el-header height="35%">
          <el-descriptions :title="$t('phytoplankto.sampleDataInfo')">
            <el-descriptions-item :label="$t('phytoplankto.samplingLocation')">
              <el-tag> {{ samplingInfo.samplingLocation }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.sampleVolumeLiters')">{{ samplingInfo.sampleVolumeLiters }}(L)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.concentratedVolume')">{{ samplingInfo.concentratedVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.dilutionFactor')">{{ samplingInfo.dilutionFactor }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.fieldOfViewCount')">{{ samplingInfo.fieldOfViewCount ? samplingInfo.fieldOfViewCount + "(个)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.fieldArea')">{{ samplingInfo.fieldArea ? samplingInfo.fieldArea + "(mm2)" : $t("noData") }}</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingArea')">{{ samplingInfo.countingArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingFrameVolume')">{{ samplingInfo.countingFrameVolume }}(mL)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.countingFrameArea')">{{ samplingInfo.countingFrameArea }}(mm2)</el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.samplingAt')">
              <el-tag type="success"> {{ samplingInfo.samplingAt }} </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('phytoplankto.dataSource')">
              <el-tag type="warning"> {{ samplingInfo.dataSource }} </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-header>
        <el-main>
          <el-form :inline="true" :model="samplingTestData.dataForm" @keyup.enter="samplingTestData.getDataList()">
            <el-form-item>
              <el-form-item>
                <el-input style="width: 200px" v-model="samplingTestData.dataForm.species" :placeholder="$t('phytoplankto.species')" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-button @click="samplingTestData.getDataList()">{{ $t("query") }}</el-button>
              </el-form-item>
              <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="samplingTestData.deleteHandle()">{{ $t("delete") }}</el-button>
            </el-form-item>
          </el-form>
          <el-table v-loading="samplingTestData.dataListLoading" :data="samplingTestData.dataList" border @selection-change="samplingTestData.dataListSelectionChangeHandle" style="width: 100%">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingId" :label="$t('phytoplankto.test1')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="stationId" :label="$t('phytoplankto.test2')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="stationName" :label="$t('phytoplankto.test3')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeYear" :label="$t('phytoplankto.test4')" header-align="center" align="center"></el-table-column>
            <el-table-column width="200" prop="timeMonth" :label="$t('phytoplankto.test5')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDay" :label="$t('phytoplankto.test6')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDetail" :label="$t('phytoplankto.test7')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingVolume" :label="$t('phytoplankto.test8')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="concentratedVolume" :label="$t('phytoplankto.test9')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="dilutionRatio" :label="$t('phytoplankto.test10')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="fieldNumber" :label="$t('phytoplankto.test11')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="fieldArea" :label="$t('phytoplankto.test12')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoPhylum" :label="$t('phytoplankto.test13')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoClass" :label="$t('phytoplankto.test14')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoOrder" :label="$t('phytoplankto.test15')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoFamily" :label="$t('phytoplankto.test16')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoGenus" :label="$t('phytoplankto.test17')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoCategory" :label="$t('phytoplankto.test18')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoLatin" :label="$t('phytoplankto.test19')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="wetWeight" :label="$t('phytoplankto.test20')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="phytoNumber" :label="$t('phytoplankto.test21')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="cellNumber" :label="$t('phytoplankto.test22')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="biomass" :label="$t('phytoplankto.test23')" header-align="center" align="center"></el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="samplingTestData.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="samplingTestData.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="samplingTestData.limit"
            :total="samplingTestData.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="samplingTestData.pageSizeChangeHandle"
            @current-change="samplingTestData.pageCurrentChangeHandle"
          ></el-pagination>
        </el-main>
      </el-container>
      <el-container style="justify-content: center" v-else> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
    </el-container>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="samplingTestData.getDataList"></add-or-update>
    <show-or-update ref="showOrUpdateRef" @refreshDataList="sampling.getDataList"></show-or-update>
    <add-or-update-sample ref="addOrUpdateSampleRef" @refreshDataList="sampling.getDataList"></add-or-update-sample>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onMounted, reactive, ref, toRefs} from "vue";
import ShowOrUpdate from "./sampling-show-or-update.vue";
import AddOrUpdate from "./phytodata-add-or-update.vue";
import AddOrUpdateSample from "./phytosampledata-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import baseService from "@/service/baseService";
import { ElTree } from "element-plus";
import base from "@/router/base";
const { $t } = globalLanguage();
const view1 = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/monitoringstation/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytosampledata/export",
  deleteURL: "/bga/phytosampledata",
  dataForm: {
    id: "",
    samplingLocation: "",
    createDate: "",
    samplingAt: ""
  }
});
interface SamplingInfo {
  id: string;
  samplingId: string;
  stationId: string;
  stationName: string;
  timeYear: string;
  timeMonth: string;
  fieldNumber: string;
  timeDay: string;
  fieldArea: string;
  phytoPhylum: string;
  phytoClass: string;
  phytoOrder: string;
  phytoFamily: string;
  phytoGenus: string;
  phytoCategory: string;
  phytoLatin: string;
  wetWeight: string;
  phytoNumber: string;
  cellNumber: string;
  biomass: string;
  timeDetail: string;
  samplingVolume: string;
  concentratedVolume: string;
  samplingLocation?: string; // 可选属性
  sampleVolumeLiters?: string; // 可选属性
  dilutionFactor?: string; // 可选属性
  fieldOfViewCount?: string; // 可选属性
  countingArea?: string; // 可选属性
  countingFrameVolume?: string; // 可选属性
  countingFrameArea?: string; // 可选属性
  samplingAt?: string; // 可选属性
  dataSource?: string; // 可选属性
  species?: string; // 可选属性
  dilutionRatio?: string; // 可选属性
  // 其他属性...
}

const view2 = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bga/phytodatanew/page",
  getDataListIsPage: true,
  exportURL: "/bga/phytodatanew/export",
  deleteURL: "/bga/phytodatanew",
  dataForm: {
    samplingId: 0,
    stationName: "",
    timeYear: "",
    timeMonth: "",
    phytoCategory: ""
  }
});

const samplingTestData = reactive<any>({ ...useView(view2), ...toRefs(view2) });
const samplingInfo = ref<SamplingInfo>({
  id: "",
  samplingId: "",
  stationId: "",
  stationName: "",
  timeYear: "",
  timeMonth: "",
  timeDay: "",
  timeDetail: "",
  samplingVolume: "",
  concentratedVolume: "",
  dilutionRatio: "",
  fieldNumber: "",
  fieldArea: "",
  phytoPhylum: "",
  phytoClass: "",
  phytoOrder: "",
  phytoFamily: "",
  phytoGenus: "",
  phytoCategory: "",
  phytoLatin: "",
  wetWeight: "",
  phytoNumber: "",
  cellNumber: "",
  biomass: ""
});
const sampling = reactive({ ...useView(view1), ...toRefs(view1) });
const sampling_test = reactive({ ...useView(view2), ...toRefs(view2) });
const selectSample = ref(1);
const getSamplingInfo = (info: any) => {
  selectSample.value = info.stationId;
  Object.assign(samplingInfo.value, info);
  view2.dataForm.samplingId = info.stationId;
  samplingTestData.getDataList();
};
const showOrUpdateRef = ref();
const showOrUpdateHandle = (id: any, type: string) => {
  showOrUpdateRef.value.init(id, type);
};
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const addOrUpdateSampleRef = ref();
const addOrUpdateHandleData = (id?: number) => {
  addOrUpdateSampleRef.value.init(id);
};


const defaultProps = {
  children: 'children',
  label: 'label',
};
baseService.get("/bga/phytodatanew/page", { limit: 50 }).then((test) => {
  console.log(test);
  console.log(test.data);
});

interface TreeNode {
  id: string | number; // 假设id是唯一的，可以用作node-key
  label: string;
  children?: TreeNode[]; // 可选的子节点数组
  data?: any; // 原始数据，如果需要可以在节点上附加
}
const treeData = ref<TreeNode[]>([]);
const processData = (dataList: any[]): TreeNode[] => {
  const yearMap = new Map<string, TreeNode>();

  dataList.forEach(item => {
    const year = item.timeYear;
    const month = item.timeMonth;
    const name = item.stationName;

    if (!yearMap.has(year)) {
      yearMap.set(year, {
        id: year, // 使用年份作为id
        label: year,
        children: []
      });
    }

    const yearNode : any = yearMap.get(year)!;
    let monthNode = yearNode.children.find((child : any) => child.label === month);
    if (!monthNode) {
      monthNode = {
        id: `${year}-${month}`, // 使用年份和月份的组合作为id
        label: month,
        children: [], // 这里可以放置更详细的节点，或者留空
        data: item // 附加原始数据
      };
      yearNode.children.push(monthNode);
    } else {
      // 如果需要，可以在这里处理重复的月份节点（比如合并数据）
      // 但在这个例子中，我们假设每个月份只出现一次
    }
    let stationNode = monthNode.children.find((child : any) => child.label === name);
    if (!stationNode) {
      stationNode = {
        id: `${year}-${month}-${name}`,
        label: name,
        children: [],
        isLeaf: 'leaf'
        // 如果没有更详细的子节点，这里可以留空
      };
      monthNode.children.push(stationNode);
      // 如果需要附加原始数据，可以在这里添加
      // stationNode.data = item;
    }
  });

  return [...yearMap.values()];
};
const fetchData = async () => {
  try {
    const response = await baseService.get("/bga/phytodatanew/page", { limit: 50 });
    const dataList = response.data.list;
    treeData.value = processData(dataList);
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
// 表格数据
const dataList = ref<any[]>([]);
// 表格数据加载状态
const dataListLoading = ref(false);
const handleNodeClick = async (data: { id: string; label: string; children?: any[]; isLeaf?: string; $treeNodeId?: number }) => {
  if(data.children && data.children.length === 0 && data.isLeaf === 'leaf'){
    try {
      // 解析 id 为 timeYear, timeMonth, stationName
      const [timeYear, timeMonth, stationName] = data.id.split('-');
      samplingTestData.dataForm.timeYear = timeYear;
      samplingTestData.dataForm.timeMonth = timeMonth;
      samplingTestData.dataForm.stationName = stationName;
      samplingTestData.getDataList();
    } catch (error) {
      console.error('查询失败:', error);
      // 可以向用户显示错误消息，例如使用 ElMessage.error
    } finally {
      dataListLoading.value = false;
    }
  }
};
onMounted(fetchData);
</script>

<style scoped lang="less">
@import "../../assets/theme/base.less";
.card-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.scrollbar {
  height: 3000px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.el-scrollbar__view .el-card:hover {
  color: @light-text-active;
}

.el-scrollbar__view .el-card.active {
  background: @light-bg-active;
  border: 1px solid @light-text-active;
  color: @light-text-active;
}
.el-card {
  cursor: pointer;
  border-radius: 16px;
  width: 100%;
  margin-top: 5%;
}
</style>
